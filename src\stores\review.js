import { defineStore } from 'pinia'
import { reviewApi, statsApi } from '@/api'
import { ElMessage } from 'element-plus'
import Logger from '@/utils/logger'

export const useReviewStore = defineStore('review', {
  state: () => ({
    reviewHistory: [],
    stats: {
      totalReviews: 0,
      avgScore: 0,
      todayReviews: 0,
      weekReviews: 0
    },
    categoryStats: [],
    difficultyStats: [],
    trendData: [],
    loading: false
  }),

  getters: {
    // 获取今日复习统计
    todayStats: (state) => {
      const today = new Date().toDateString()
      const todayReviews = state.reviewHistory.filter(
        review => new Date(review.createdAt).toDateString() === today
      )
      
      return {
        count: todayReviews.length,
        avgScore: todayReviews.length > 0 
          ? todayReviews.reduce((sum, r) => sum + r.score, 0) / todayReviews.length 
          : 0
      }
    },

    // 获取本周复习统计
    weekStats: (state) => {
      const oneWeekAgo = new Date()
      oneWeekAgo.setDate(oneWeekAgo.getDate() - 7)
      
      const weekReviews = state.reviewHistory.filter(
        review => new Date(review.createdAt) >= oneWeekAgo
      )
      
      return {
        count: weekReviews.length,
        avgScore: weekReviews.length > 0 
          ? weekReviews.reduce((sum, r) => sum + r.score, 0) / weekReviews.length 
          : 0
      }
    },

    // 需要复习的题目数量（评分低于3分的）
    needReviewCount: (state) => {
      return state.reviewHistory.filter(review => review.score < 3).length
    }
  },

  actions: {
    // 提交复习记录
    async submitReview(reviewData) {
      try {
        const response = await reviewApi.submitReview(reviewData)
        if (response.success) {
          ElMessage.success(`复习完成，评分：${reviewData.masteryScore || reviewData.score}分`)
          // 更新本地记录
          this.reviewHistory.unshift(response.data)
          await this.fetchStats()
          return { success: true }
        }
        return { success: false, message: response.message }
      } catch (error) {
        ElMessage.error('提交复习记录失败')
        return { success: false, message: '网络错误' }
      }
    },

    // 获取复习记录
    async fetchReviewHistory(params = {}) {
      this.loading = true
      try {
        const response = await reviewApi.getRecentReviews(params)
        if (response.success) {
          this.reviewHistory = response.data || []
        }
      } catch (error) {
        Logger.error('获取复习记录失败:', error)
      } finally {
        this.loading = false
      }
    },

    // 获取统计数据
    async fetchStats() {
      try {
        const [overallResponse, categoryResponse, difficultyResponse, trendResponse] = await Promise.all([
          statsApi.getOverallStats(),
          statsApi.getCategoryStats(),
          statsApi.getDifficultyStats(),
          statsApi.getReviewTrend({ days: 30 })
        ])

        if (overallResponse.success) {
          this.stats = overallResponse.data
        }

        if (categoryResponse.success) {
          this.categoryStats = categoryResponse.data || []
        }

        if (difficultyResponse.success) {
          this.difficultyStats = difficultyResponse.data || []
        }

        if (trendResponse.success) {
          this.trendData = trendResponse.data || []
        }
      } catch (error) {
        Logger.error('获取统计数据失败:', error)
      }
    },

    // 获取复习趋势数据
    async fetchTrendData(days = 7) {
      try {
        const response = await statsApi.getReviewTrend({ days })
        if (response.success) {
          this.trendData = response.data || []
        }
      } catch (error) {
        Logger.error('获取趋势数据失败:', error)
      }
    }
  }
})