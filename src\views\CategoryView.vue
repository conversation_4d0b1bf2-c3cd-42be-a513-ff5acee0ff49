<template>
  <div class="category-management-container">
    <!-- 页面头部 -->
    <el-card class="header-card" shadow="never">
      <div class="header-content">
        <div class="page-title">
          <el-icon :size="24"><Management /></el-icon>
          <h2>分类管理</h2>
        </div>
        <el-button type="primary" :icon="Plus" @click="handleAdd">
          新增分类
        </el-button>
      </div>
    </el-card>

    <!-- 分类树表格 -->
    <el-card class="table-card" shadow="never">
      <el-table
        :data="categoryStore.categoryTree"
        v-loading="categoryStore.loading"
        row-key="id"
        default-expand-all
        :tree-props="{ children: 'children', hasChildren: 'hasChildren' }"
        class="category-table"
      >
        <el-table-column prop="name" label="分类名称" min-width="200">
          <template #default="{ row }">
            <div class="category-name">
              <el-icon v-if="!row.parentId" :size="16" class="category-icon">
                <Folder />
              </el-icon>
              <el-icon v-else :size="16" class="category-icon">
                <FolderOpened />
              </el-icon>
              <span>{{ row.name }}</span>
            </div>
          </template>
        </el-table-column>
        
        <el-table-column prop="description" label="描述" min-width="200">
          <template #default="{ row }">
            <span class="description-text">{{ row.description || '-' }}</span>
          </template>
        </el-table-column>
        
        <el-table-column prop="questionCount" label="题目数量" width="120" align="center">
          <template #default="{ row }">
            <el-tag type="info" size="small">{{ row.questionCount || 0 }}</el-tag>
          </template>
        </el-table-column>
        
        <el-table-column prop="sortOrder" label="排序" width="100" align="center">
          <template #default="{ row }">
            <span>{{ row.sortOrder || 0 }}</span>
          </template>
        </el-table-column>
        
        <el-table-column prop="createTime" label="创建时间" width="180">
          <template #default="{ row }">
            <span>{{ formatDate(row.createTime) }}</span>
          </template>
        </el-table-column>
        
        <el-table-column label="操作" width="200" fixed="right">
          <template #default="{ row }">
            <div class="action-buttons">
              <el-button
                link
                type="primary"
                :icon="Plus"
                @click="handleAddChild(row)"
                size="small"
              >
                添加子分类
              </el-button>
              <el-button
                link
                type="primary"
                :icon="Edit"
                @click="handleEdit(row)"
                size="small"
              >
                编辑
              </el-button>
              <el-button
                link
                type="danger"
                :icon="Delete"
                @click="handleDelete(row)"
                size="small"
              >
                删除
              </el-button>
            </div>
          </template>
        </el-table-column>
      </el-table>
    </el-card>

    <!-- 新增/编辑分类对话框 -->
    <el-dialog
      v-model="dialogVisible"
      :title="dialogTitle"
      width="500px"
      :before-close="handleDialogClose"
    >
      <el-form
        ref="formRef"
        :model="formData"
        :rules="formRules"
        label-width="80px"
      >
        <el-form-item label="分类名称" prop="name">
          <el-input
            v-model="formData.name"
            placeholder="请输入分类名称"
            maxlength="50"
            show-word-limit
          />
        </el-form-item>
        
        <el-form-item label="父分类" prop="parentId">
          <el-select
            v-model="formData.parentId"
            placeholder="请选择父分类（可选）"
            clearable
            style="width: 100%"
          >
            <el-option
              v-for="category in categoryStore.categories"
              :key="category.id"
              :label="category.name"
              :value="category.id"
              :disabled="category.id === formData.id"
            />
          </el-select>
        </el-form-item>
        
        <el-form-item label="描述" prop="description">
          <el-input
            v-model="formData.description"
            type="textarea"
            :rows="3"
            placeholder="请输入分类描述（可选）"
            maxlength="200"
            show-word-limit
          />
        </el-form-item>
      </el-form>
      
      <template #footer>
        <div class="dialog-footer">
          <el-button @click="handleDialogClose">取消</el-button>
          <el-button type="primary" @click="handleSubmit" :loading="submitting">
            确定
          </el-button>
        </div>
      </template>
    </el-dialog>
  </div>
</template>

<script setup>
import { ref, reactive, onMounted } from 'vue'
import { ElMessage, ElMessageBox } from 'element-plus'
import { 
  Management, Plus, Edit, Delete, 
  Folder, FolderOpened 
} from '@element-plus/icons-vue'
import { useCategoryStore } from '@/stores/category'

// Store
const categoryStore = useCategoryStore()

// 响应式数据
const dialogVisible = ref(false)
const dialogTitle = ref('新增分类')
const submitting = ref(false)
const formRef = ref(null)
const isEdit = ref(false)

// 表单数据
const formData = reactive({
  id: null,
  name: '',
  parentId: null,
  description: ''
})

// 表单验证规则
const formRules = {
  name: [
    { required: true, message: '请输入分类名称', trigger: 'blur' },
    { min: 1, max: 50, message: '长度在 1 到 50 个字符', trigger: 'blur' }
  ]
}

// 方法
const formatDate = (dateString) => {
  if (!dateString) return '-'
  const date = new Date(dateString)
  return date.toLocaleDateString('zh-CN', {
    year: 'numeric',
    month: '2-digit',
    day: '2-digit',
    hour: '2-digit',
    minute: '2-digit'
  })
}

const resetForm = () => {
  formData.id = null
  formData.name = ''
  formData.parentId = null
  formData.description = ''
  
  if (formRef.value) {
    formRef.value.resetFields()
  }
}

const handleAdd = () => {
  resetForm()
  isEdit.value = false
  dialogTitle.value = '新增分类'
  dialogVisible.value = true
}

const handleAddChild = (parentCategory) => {
  resetForm()
  formData.parentId = parentCategory.id
  isEdit.value = false
  dialogTitle.value = `添加 "${parentCategory.name}" 的子分类`
  dialogVisible.value = true
}

const handleEdit = (category) => {
  resetForm()
  formData.id = category.id
  formData.name = category.name
  formData.parentId = category.parentId
  formData.description = category.description || ''
  
  isEdit.value = true
  dialogTitle.value = '编辑分类'
  dialogVisible.value = true
}

const handleDelete = async (category) => {
  try {
    await ElMessageBox.confirm(
      `确认删除分类 "${category.name}" 吗？删除后该分类下的所有题目将需要重新分类。`,
      '删除确认',
      {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }
    )
    
    const result = await categoryStore.deleteCategory(category.id)
    if (result.success) {
      await loadData()
    }
  } catch (error) {
    // 用户取消删除
  }
}

const handleSubmit = async () => {
  if (!formRef.value) return
  
  try {
    await formRef.value.validate()
    submitting.value = true
    
    const submitData = {
      name: formData.name,
      parentId: formData.parentId || null,
      description: formData.description || null
    }
    
    let result
    if (isEdit.value) {
      result = await categoryStore.updateCategory(formData.id, submitData)
    } else {
      result = await categoryStore.createCategory(submitData)
    }
    
    if (result.success) {
      dialogVisible.value = false
      await loadData()
    }
  } catch (error) {
    // 表单验证失败
  } finally {
    submitting.value = false
  }
}

const handleDialogClose = () => {
  dialogVisible.value = false
  resetForm()
}

const loadData = async () => {
  await Promise.all([
    categoryStore.fetchCategories(),
    categoryStore.fetchCategoryTree()
  ])
}

// 生命周期
onMounted(() => {
  loadData()
})
</script>

<style scoped>
.category-management-container {
  padding: 20px;
}

.header-card {
  margin-bottom: 20px;
}

.header-content {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.page-title {
  display: flex;
  align-items: center;
  gap: 12px;
}

.page-title h2 {
  margin: 0;
  font-size: 24px;
  color: #303133;
  font-weight: 600;
}

.category-table {
  .category-name {
    display: flex;
    align-items: center;
    gap: 8px;
  }
  
  .category-icon {
    color: #409eff;
  }
  
  .description-text {
    color: #606266;
    line-height: 1.4;
  }
  
  .action-buttons {
    display: flex;
    gap: 8px;
    flex-wrap: wrap;
  }
}

.dialog-footer {
  display: flex;
  justify-content: flex-end;
  gap: 12px;
}

@media (max-width: 768px) {
  .category-management-container {
    padding: 10px;
  }
  
  .header-content {
    flex-direction: column;
    gap: 16px;
    align-items: stretch;
  }
  
  .page-title {
    justify-content: center;
  }
  
  .action-buttons {
    flex-direction: column;
    gap: 4px;
  }
}
</style>