import { createRouter, createWebHistory } from 'vue-router'
import { useAuthStore } from '@/stores/auth'
import Logger from '@/utils/logger'

// 导入页面组件
const LoginView = () => import('@/views/LoginView.vue')
const LayoutView = () => import('@/components/LayoutView.vue')
const DashboardView = () => import('@/views/DashboardView.vue')
const QuestionBankView = () => import('@/views/QuestionBankView.vue')
const CategoryView = () => import('@/views/CategoryView.vue')
const ReviewView = () => import('@/views/ReviewView.vue')
const StatsView = () => import('@/views/StatsView.vue')

const routes = [
  {
    path: '/login',
    name: 'Login',
    component: LoginView,
    meta: { requiresAuth: false }
  },
  {
    path: '/',
    component: LayoutView,
    meta: { requiresAuth: true },
    children: [
      {
        path: '',
        redirect: '/dashboard'
      },
      {
        path: '/dashboard',
        name: 'Dashboard',
        component: DashboardView,
        meta: { 
          requiresAuth: true,
          title: '仪表板',
          icon: 'House'
        }
      },
      {
        path: '/questions',
        name: 'QuestionBank',
        component: QuestionBankView,
        meta: { 
          requiresAuth: true,
          title: '题库管理',
          icon: 'Management'
        }
      },
      {
        path: '/categories',
        name: 'Categories',
        component: CategoryView,
        meta: { 
          requiresAuth: true,
          title: '分类管理',
          icon: 'FolderOpened'
        }
      },
      {
        path: '/review',
        name: 'Review',
        component: ReviewView,
        meta: { 
          requiresAuth: true,
          title: '开始复习',
          icon: 'Reading'
        }
      },
      {
        path: '/stats',
        name: 'Stats',
        component: StatsView,
        meta: { 
          requiresAuth: true,
          title: '学习统计',
          icon: 'DataAnalysis'
        }
      }
    ]
  },
  {
    path: '/:pathMatch(.*)*',
    redirect: '/dashboard'
  }
]

const router = createRouter({
  history: createWebHistory(import.meta.env.BASE_URL),
  routes
})

// 路由守卫
router.beforeEach((to, from, next) => {
  const authStore = useAuthStore()
  
  // 只在应用初始化时调用一次initAuth
  if (!authStore.isLoggedIn && authStore.token) {
    authStore.initAuth()
  }
  
  Logger.route(`导航至: ${to.path}, 认证状态: ${authStore.checkLoginStatus}`)
  
  if (to.meta.requiresAuth && !authStore.checkLoginStatus) {
    // 需要认证但未登录，跳转到登录页
    Logger.route('需要认证，跳转登录页')
    next('/login')
  } else if (to.path === '/login' && authStore.checkLoginStatus) {
    // 已登录用户访问登录页，跳转到首页
    Logger.route('已登录，跳转仪表板')
    next('/dashboard')
  } else {
    Logger.route('正常跳转')
    next()
  }
})

export default router
