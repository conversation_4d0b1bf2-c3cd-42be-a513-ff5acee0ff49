import { defineStore } from 'pinia'
import { categoryApi } from '@/api'
import { ElMessage } from 'element-plus'
import Logger from '@/utils/logger'

export const useCategoryStore = defineStore('category', {
  state: () => ({
    categories: [],
    categoryTree: [],
    currentCategory: null,
    loading: false
  }),

  getters: {
    // 获取顶级分类
    topCategories: (state) => {
      return state.categories.filter(cat => !cat.parentId)
    },

    // 获取分类名称映射
    categoryMap: (state) => {
      const map = {}
      state.categories.forEach(cat => {
        map[cat.id] = cat.name
      })
      return map
    },

    // 根据ID获取分类
    getCategoryById: (state) => {
      return (id) => state.categories.find(cat => cat.id === id)
    },

    // 获取子分类
    getChildCategories: (state) => {
      return (parentId) => state.categories.filter(cat => cat.parentId === parentId)
    }
  },

  actions: {
    // 获取所有分类
    async fetchCategories() {
      this.loading = true
      try {
        const response = await categoryApi.getCategories()
        if (response.success) {
          this.categories = response.data || []
        }
      } catch (error) {
        Logger.error('获取分类列表失败:', error)
        ElMessage.error('获取分类列表失败')
      } finally {
        this.loading = false
      }
    },

    // 获取分类树
    async fetchCategoryTree() {
      try {
        const response = await categoryApi.getCategoryTree()
        if (response.success) {
          this.categoryTree = response.data || []
        }
      } catch (error) {
        Logger.error('获取分类树失败:', error)
      }
    },

    // 获取顶级分类
    async fetchTopCategories() {
      try {
        const response = await categoryApi.getTopCategories()
        if (response.success) {
          // 更新categories中的顶级分类
          const topCats = response.data || []
          topCats.forEach(topCat => {
            const index = this.categories.findIndex(cat => cat.id === topCat.id)
            if (index >= 0) {
              this.categories[index] = topCat
            } else {
              this.categories.push(topCat)
            }
          })
        }
      } catch (error) {
        Logger.error('获取顶级分类失败:', error)
      }
    },

    // 获取子分类
    async fetchChildCategories(parentId) {
      try {
        const response = await categoryApi.getChildCategories(parentId)
        if (response.success) {
          const childCats = response.data || []
          // 更新categories中的子分类
          childCats.forEach(childCat => {
            const index = this.categories.findIndex(cat => cat.id === childCat.id)
            if (index >= 0) {
              this.categories[index] = childCat
            } else {
              this.categories.push(childCat)
            }
          })
        }
      } catch (error) {
        Logger.error('获取子分类失败:', error)
      }
    },

    // 创建分类
    async createCategory(categoryData) {
      try {
        const response = await categoryApi.createCategory(categoryData)
        if (response.success) {
          ElMessage.success('分类创建成功')
          await this.fetchCategories() // 刷新分类列表
          return { success: true }
        }
        return { success: false, message: response.message }
      } catch (error) {
        Logger.error('创建分类失败:', error)
        ElMessage.error('创建分类失败')
        return { success: false, message: '网络错误' }
      }
    },

    // 更新分类
    async updateCategory(id, categoryData) {
      try {
        const response = await categoryApi.updateCategory(id, categoryData)
        if (response.success) {
          ElMessage.success('分类更新成功')
          await this.fetchCategories() // 刷新分类列表
          return { success: true }
        }
        return { success: false, message: response.message }
      } catch (error) {
        Logger.error('更新分类失败:', error)
        ElMessage.error('更新分类失败')
        return { success: false, message: '网络错误' }
      }
    },

    // 删除分类
    async deleteCategory(id) {
      try {
        const response = await categoryApi.deleteCategory(id)
        if (response.success) {
          ElMessage.success('分类删除成功')
          await this.fetchCategories() // 刷新分类列表
          return { success: true }
        }
        return { success: false, message: response.message }
      } catch (error) {
        Logger.error('删除分类失败:', error)
        ElMessage.error('删除分类失败')
        return { success: false, message: '网络错误' }
      }
    },

    // 获取分类详情
    async getCategoryDetails(id) {
      try {
        const response = await categoryApi.getCategory(id)
        if (response.success) {
          this.currentCategory = response.data
          return { success: true, data: response.data }
        }
        return { success: false, message: response.message }
      } catch (error) {
        Logger.error('获取分类详情失败:', error)
        return { success: false, message: '网络错误' }
      }
    },

    // 设置当前分类
    setCurrentCategory(category) {
      this.currentCategory = category
    },

    // 清空当前分类
    clearCurrentCategory() {
      this.currentCategory = null
    }
  }
})