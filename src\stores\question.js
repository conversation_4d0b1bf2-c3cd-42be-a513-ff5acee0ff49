import { defineStore } from 'pinia'
import { questionApi } from '@/api'
import { ElMessage } from 'element-plus'
import Logger from '@/utils/logger'

export const useQuestionStore = defineStore('question', {
  state: () => ({
    questions: [],
    currentQuestion: null,
    loading: false,
    total: 0,
    page: 0, // 改为从0开始
    pageSize: 10,
    filters: {
      categoryId: null, // category 改为 categoryId
      difficulty: null, // 改为数字
      keyword: ''
    }
  }),

  getters: {
    // 获取过滤后的题目
    filteredQuestions: (state) => {
      let filtered = state.questions
      
      if (state.filters.categoryId) {
        filtered = filtered.filter(q => q.categoryId === state.filters.categoryId)
      }
      
      if (state.filters.difficulty) {
        filtered = filtered.filter(q => q.difficulty === state.filters.difficulty)
      }
      
      if (state.filters.keyword) {
        const keyword = state.filters.keyword.toLowerCase()
        filtered = filtered.filter(q => 
          q.title.toLowerCase().includes(keyword) || 
          q.content.toLowerCase().includes(keyword)
        )
      }
      
      return filtered
    },

    // 获取难度统计
    difficultyStats: (state) => {
      const stats = { 1: 0, 2: 0, 3: 0 } // 1-简单，2-中等，3-困难
      state.questions.forEach(q => {
        if (stats[q.difficulty] !== undefined) {
          stats[q.difficulty]++
        }
      })
      return stats
    },

    // 获取分类统计
    categoryStats: (state) => {
      const stats = {}
      state.questions.forEach(q => {
        stats[q.categoryId] = (stats[q.categoryId] || 0) + 1
      })
      return stats
    },

    // 难度文字映射
    difficultyText: () => {
      return {
        1: '简单',
        2: '中等', 
        3: '困难'
      }
    }
  },

  actions: {
    // 获取题目列表
    async fetchQuestions(params = {}) {
      this.loading = true
      try {
        // 合并分页参数
        const requestParams = {
          page: this.page,
          pageSize: this.pageSize,
          ...params
        }
        
        const response = await questionApi.getQuestions(requestParams)
        if (response.success) {
          this.questions = response.data.questions || []
          this.total = response.data.total || 0
          this.page = response.data.page || 0
          this.pageSize = response.data.pageSize || 10
        }
      } catch (error) {
        Logger.error('获取题目列表失败:', error)
        ElMessage.error('获取题目列表失败')
      } finally {
        this.loading = false
      }
    },

    // 搜索题目
    async searchQuestions(params = {}) {
      this.loading = true
      try {
        const response = await questionApi.searchQuestions(params)
        if (response.success) {
          this.questions = response.data.questions || []
          this.total = response.data.total || 0
        }
      } catch (error) {
        Logger.error('搜索题目失败:', error)
        ElMessage.error('搜索题目失败')
      } finally {
        this.loading = false
      }
    },

    // 根据分类获取题目
    async fetchQuestionsByCategory(categoryId, params = {}) {
      this.loading = true
      try {
        const response = await questionApi.getQuestionsByCategory(categoryId, params)
        if (response.success) {
          this.questions = response.data.questions || []
          this.total = response.data.total || 0
        }
      } catch (error) {
        Logger.error('获取分类题目失败:', error)
        ElMessage.error('获取分类题目失败')
      } finally {
        this.loading = false
      }
    },

    // 根据难度获取题目
    async fetchQuestionsByDifficulty(difficulty, params = {}) {
      this.loading = true
      try {
        const response = await questionApi.getQuestionsByDifficulty(difficulty, params)
        if (response.success) {
          this.questions = response.data.questions || []
          this.total = response.data.total || 0
        }
      } catch (error) {
        Logger.error('获取难度题目失败:', error)
        ElMessage.error('获取难度题目失败')
      } finally {
        this.loading = false
      }
    },

    // 创建题目
    async createQuestion(questionData) {
      try {
        const response = await questionApi.createQuestion(questionData)
        if (response.success) {
          ElMessage.success('题目创建成功')
          await this.fetchQuestions()
          return { success: true }
        }
        return { success: false, message: response.message }
      } catch (error) {
        Logger.error('创建题目失败:', error)
        ElMessage.error('创建题目失败')
        return { success: false, message: '网络错误' }
      }
    },

    // 更新题目
    async updateQuestion(id, questionData) {
      try {
        const response = await questionApi.updateQuestion(id, questionData)
        if (response.success) {
          ElMessage.success('题目更新成功')
          await this.fetchQuestions()
          return { success: true }
        }
        return { success: false, message: response.message }
      } catch (error) {
        Logger.error('更新题目失败:', error)
        ElMessage.error('更新题目失败')
        return { success: false, message: '网络错误' }
      }
    },

    // 删除题目
    async deleteQuestion(id) {
      try {
        const response = await questionApi.deleteQuestion(id)
        if (response.success) {
          ElMessage.success('题目删除成功')
          await this.fetchQuestions()
          return { success: true }
        }
        return { success: false, message: response.message }
      } catch (error) {
        Logger.error('删除题目失败:', error)
        ElMessage.error('删除题目失败')
        return { success: false, message: '网络错误' }
      }
    },

    // 批量删除题目
    async batchDeleteQuestions(ids) {
      try {
        const response = await questionApi.batchDeleteQuestions(ids)
        if (response.success) {
          ElMessage.success('题目批量删除成功')
          await this.fetchQuestions()
          return { success: true }
        }
        return { success: false, message: response.message }
      } catch (error) {
        Logger.error('批量删除题目失败:', error)
        ElMessage.error('批量删除题目失败')
        return { success: false, message: '网络错误' }
      }
    },

    // 获取随机题目用于复习
    async getRandomQuestion(params = {}) {
      try {
        const response = await questionApi.getRandomQuestion(params)
        if (response.success) {
          this.currentQuestion = response.data
          return { success: true, data: response.data }
        }
        return { success: false, message: response.message }
      } catch (error) {
        Logger.error('获取随机题目失败:', error)
        ElMessage.error('获取题目失败')
        return { success: false, message: '网络错误' }
      }
    },

    // 获取待复习题目
    async getReviewQuestions(params = {}) {
      try {
        const response = await questionApi.getReviewQuestions(params)
        if (response.success) {
          return { success: true, data: response.data }
        }
        return { success: false, message: response.message }
      } catch (error) {
        Logger.error('获取待复习题目失败:', error)
        return { success: false, message: '网络错误' }
      }
    },

    // 设置过滤条件
    setFilters(filters) {
      this.filters = { ...this.filters, ...filters }
    },

    // 清除过滤条件
    clearFilters() {
      this.filters = {
        categoryId: null,
        difficulty: null,
        keyword: ''
      }
    },

    // 设置分页
    setPagination(page, pageSize) {
      this.page = page
      this.pageSize = pageSize
    },

    // 设置当前题目
    setCurrentQuestion(question) {
      this.currentQuestion = question
    },

    // 清空当前题目
    clearCurrentQuestion() {
      this.currentQuestion = null
    }
  }
})