<template>
  <div class="dashboard-container">
    <!-- 欢迎横幅 -->
    <el-card class="welcome-card" shadow="never">
      <div class="welcome-content">
        <div class="welcome-text">
          <h2 class="welcome-title">
            👋 欢迎回来，{{ userStore.user?.username || '学习者' }}！
          </h2>
          <p class="welcome-subtitle">
            坚持每日复习，掌握面试技能。今天也要加油哦！
          </p>
        </div>
        <div class="welcome-action">
          <el-button 
            type="primary" 
            size="large"
            @click="startReview"
            :icon="Reading"
          >
            开始今日复习
          </el-button>
        </div>
      </div>
    </el-card>

    <!-- 统计卡片 -->
    <div class="stats-grid">
      <el-card 
        v-for="stat in statsCards" 
        :key="stat.key"
        class="stat-card"
        shadow="hover"
        @click="stat.action && stat.action()"
        :class="{ 'clickable': stat.action }"
      >
        <div class="stat-content">
          <div class="stat-icon" :style="{ background: stat.color }">
            <el-icon :size="24">
              <component :is="stat.icon" />
            </el-icon>
          </div>
          <div class="stat-info">
            <div class="stat-number">{{ stat.value }}</div>
            <div class="stat-label">{{ stat.label }}</div>
          </div>
        </div>
      </el-card>
    </div>

    <!-- 主要内容区域 -->
    <div class="main-content">
      <el-row :gutter="20">
        <!-- 左列 -->
        <el-col :lg="16" :md="24" :sm="24" :xs="24">
          <!-- 待复习题目 -->
          <el-card class="content-card" shadow="never">
            <template #header>
              <div class="card-header">
                <h3 class="card-title">
                  <el-icon><Clock /></el-icon>
                  待复习题目
                </h3>
                <el-button 
                  type="text" 
                  @click="goToQuestions"
                  :icon="More"
                >
                  查看全部
                </el-button>
              </div>
            </template>

            <div v-if="reviewQuestions.length === 0" class="empty-state">
              <el-icon :size="48" color="#e6e8eb">
                <DocumentChecked />
              </el-icon>
              <p>恭喜！暂无需要复习的题目</p>
              <el-button type="primary" @click="startReview">
                开始新的复习
              </el-button>
            </div>

            <div v-else class="question-list">
              <div 
                v-for="question in reviewQuestions.slice(0, 5)" 
                :key="question.id"
                class="question-item"
                @click="reviewQuestion(question)"
              >
                <div class="question-info">
                  <h4 class="question-title">{{ question.title }}</h4>
                  <div class="question-meta">
                    <el-tag :type="getDifficultyType(question.difficulty)" size="small">
                      {{ getDifficultyText(question.difficulty) }}
                    </el-tag>
                    <span class="question-category">{{ question.category }}</span>
                    <span class="question-score score-{{ question.lastScore }}">
                      上次得分: {{ question.lastScore }}分
                    </span>
                  </div>
                </div>
                <el-button 
                  type="primary" 
                  size="small" 
                  :icon="Reading"
                  @click.stop="reviewQuestion(question)"
                >
                  复习
                </el-button>
              </div>
            </div>
          </el-card>

          <!-- 最近活动 -->
          <el-card class="content-card" shadow="never">
            <template #header>
              <div class="card-header">
                <h3 class="card-title">
                  <el-icon><List /></el-icon>
                  最近活动
                </h3>
              </div>
            </template>

            <el-timeline class="activity-timeline">
              <el-timeline-item
                v-for="activity in recentActivities"
                :key="activity.id"
                :timestamp="activity.time"
                :color="activity.color"
              >
                <div class="activity-item">
                  <div class="activity-content">
                    <span class="activity-action">{{ activity.action }}</span>
                    <span class="activity-target">{{ activity.target }}</span>
                    <el-tag 
                      v-if="activity.score" 
                      :type="getScoreType(activity.score)"
                      size="small"
                    >
                      {{ activity.score }}分
                    </el-tag>
                  </div>
                </div>
              </el-timeline-item>
            </el-timeline>
          </el-card>
        </el-col>

        <!-- 右列 -->
        <el-col :lg="8" :md="24" :sm="24" :xs="24">
          <!-- 今日进度 -->
          <el-card class="content-card" shadow="never">
            <template #header>
              <div class="card-header">
                <h3 class="card-title">
                  <el-icon><TrendCharts /></el-icon>
                  今日进度
                </h3>
              </div>
            </template>

            <div class="progress-content">
              <div class="progress-item">
                <div class="progress-info">
                  <span class="progress-label">复习进度</span>
                  <span class="progress-value">{{ todayProgress.completed }}/{{ todayProgress.target }}</span>
                </div>
                <el-progress 
                  :percentage="todayProgress.percentage" 
                  :color="getProgressColor(todayProgress.percentage)"
                />
              </div>

              <div class="progress-item">
                <div class="progress-info">
                  <span class="progress-label">平均得分</span>
                  <span class="progress-value">{{ todayProgress.avgScore.toFixed(1) }}分</span>
                </div>
                <el-progress 
                  :percentage="(todayProgress.avgScore / 5) * 100" 
                  :color="getScoreColor(todayProgress.avgScore)"
                />
              </div>
            </div>
          </el-card>

          <!-- 快速操作 -->
          <el-card class="content-card" shadow="never">
            <template #header>
              <div class="card-header">
                <h3 class="card-title">
                  <el-icon><Operation /></el-icon>
                  快速操作
                </h3>
              </div>
            </template>

            <div class="quick-actions">
              <el-button 
                v-for="action in quickActions"
                :key="action.key"
                :type="action.type"
                :icon="action.icon"
                class="action-button"
                @click="action.handler"
                block
              >
                {{ action.label }}
              </el-button>
            </div>
          </el-card>

          <!-- 学习提示 -->
          <el-card class="content-card tip-card" shadow="never">
            <template #header>
              <div class="card-header">
                <h3 class="card-title">
                  <el-icon><Sunny /></el-icon>
                  每日提示
                </h3>
              </div>
            </template>

            <div class="tip-content">
              <p class="tip-text">{{ dailyTip }}</p>
            </div>
          </el-card>
        </el-col>
      </el-row>
    </div>
  </div>
</template>

<script setup>
import { ref, computed, onMounted } from 'vue'
import { useRouter } from 'vue-router'
import { useAuthStore } from '@/stores/auth'
import Logger from '@/utils/logger'
import { useQuestionStore } from '@/stores/question'
import { useReviewStore } from '@/stores/review'
import { 
  Reading, Clock, More, DocumentChecked, List, TrendCharts, 
  Operation, Sunny, Management, DataAnalysis, Plus
} from '@element-plus/icons-vue'

// Store
const router = useRouter()
const userStore = useAuthStore()
const questionStore = useQuestionStore()
const reviewStore = useReviewStore()

// 响应式数据
const loading = ref(false)

// 计算属性
const statsCards = computed(() => [
  {
    key: 'totalQuestions',
    label: '题目总数',
    value: questionStore.questions.length,
    icon: 'Management',
    color: 'linear-gradient(45deg, #409eff, #66b1ff)',
    action: () => router.push('/questions')
  },
  {
    key: 'todayReviews',
    label: '今日复习',
    value: reviewStore.todayStats.count,
    icon: 'Reading',
    color: 'linear-gradient(45deg, #67c23a, #85ce61)',
    action: () => router.push('/review')
  },
  {
    key: 'avgScore',
    label: '平均得分',
    value: reviewStore.stats.avgScore?.toFixed(1) || '0.0',
    icon: 'TrendCharts',
    color: 'linear-gradient(45deg, #e6a23c, #f7ba2a)',
    action: () => router.push('/stats')
  },
  {
    key: 'needReview',
    label: '待复习',
    value: reviewStore.needReviewCount,
    icon: 'Clock',
    color: 'linear-gradient(45deg, #f56c6c, #f89898)',
    action: () => router.push('/review')
  }
])

const reviewQuestions = computed(() => {
  // 模拟待复习题目（实际应该从后端获取）
  return [
    {
      id: 1,
      title: 'JavaScript闭包的原理和应用',
      difficulty: 'medium',
      category: 'JavaScript',
      lastScore: 2
    },
    {
      id: 2,
      title: 'Vue3 Composition API详解',
      difficulty: 'hard',
      category: 'Vue.js',
      lastScore: 1
    },
    {
      id: 3,
      title: 'HTTP协议和HTTPS的区别',
      difficulty: 'easy',
      category: '网络协议',
      lastScore: 3
    }
  ]
})

const recentActivities = computed(() => {
  // 模拟最近活动
  return [
    {
      id: 1,
      action: '复习了题目',
      target: 'React Hooks使用技巧',
      score: 4,
      time: '2小时前',
      color: '#67c23a'
    },
    {
      id: 2,
      action: '添加了题目',
      target: 'TypeScript类型推断',
      time: '5小时前',
      color: '#409eff'
    },
    {
      id: 3,
      action: '复习了题目',
      target: 'CSS Grid布局',
      score: 3,
      time: '昨天',
      color: '#e6a23c'
    }
  ]
})

const todayProgress = computed(() => {
  const completed = reviewStore.todayStats.count
  const target = 10 // 每日目标
  return {
    completed,
    target,
    percentage: Math.min((completed / target) * 100, 100),
    avgScore: reviewStore.todayStats.avgScore || 0
  }
})

const quickActions = computed(() => [
  {
    key: 'review',
    label: '开始复习',
    type: 'primary',
    icon: Reading,
    handler: startReview
  },
  {
    key: 'addQuestion',
    label: '添加题目',
    type: 'success',
    icon: Plus,
    handler: addQuestion
  },
  {
    key: 'viewStats',
    label: '查看统计',
    type: 'info',
    icon: DataAnalysis,
    handler: viewStats
  }
])

const dailyTip = ref('制定学习计划，坚持每日复习，是成功的关键。记住，量变引起质变！')

// 方法
const startReview = () => {
  router.push('/review')
}

const goToQuestions = () => {
  router.push('/questions')
}

const reviewQuestion = (question) => {
  router.push(`/review?id=${question.id}`)
}

const addQuestion = () => {
  router.push('/questions?action=add')
}

const viewStats = () => {
  router.push('/stats')
}

const getDifficultyType = (difficulty) => {
  const types = {
    easy: 'success',
    medium: 'warning',
    hard: 'danger'
  }
  return types[difficulty] || 'info'
}

const getDifficultyText = (difficulty) => {
  const texts = {
    easy: '简单',
    medium: '中等',
    hard: '困难'
  }
  return texts[difficulty] || '未知'
}

const getScoreType = (score) => {
  if (score >= 4) return 'success'
  if (score >= 3) return 'warning'
  return 'danger'
}

const getProgressColor = (percentage) => {
  if (percentage >= 80) return '#67c23a'
  if (percentage >= 60) return '#e6a23c'
  return '#f56c6c'
}

const getScoreColor = (score) => {
  if (score >= 4) return '#67c23a'
  if (score >= 3) return '#e6a23c'
  return '#f56c6c'
}

const loadData = async () => {
  loading.value = true
  try {
    await Promise.all([
      questionStore.fetchQuestions(),
      reviewStore.fetchStats(),
      reviewStore.fetchReviewHistory({ limit: 10 })
    ])
  } catch (error) {
    Logger.error('加载数据失败:', error)
  } finally {
    loading.value = false
  }
}

// 生命周期
onMounted(() => {
  loadData()
})
</script>

<style scoped>
.dashboard-container {
  padding: 0;
}

/* 欢迎卡片 */
.welcome-card {
  margin-bottom: 20px;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  border: none;
}

.welcome-card :deep(.el-card__body) {
  padding: 24px;
}

.welcome-content {
  display: flex;
  align-items: center;
  justify-content: space-between;
  color: white;
}

.welcome-title {
  font-size: 24px;
  font-weight: bold;
  margin: 0 0 8px 0;
}

.welcome-subtitle {
  font-size: 14px;
  opacity: 0.9;
  margin: 0;
}

/* 统计网格 */
.stats-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
  gap: 16px;
  margin-bottom: 20px;
}

.stat-card {
  cursor: pointer;
  transition: all 0.3s ease;
}

.stat-card.clickable:hover {
  transform: translateY(-2px);
  box-shadow: 0 8px 30px rgba(0, 0, 0, 0.12);
}

.stat-content {
  display: flex;
  align-items: center;
  gap: 16px;
}

.stat-icon {
  width: 56px;
  height: 56px;
  border-radius: 12px;
  display: flex;
  align-items: center;
  justify-content: center;
  color: white;
}

.stat-info {
  flex: 1;
}

.stat-number {
  font-size: 28px;
  font-weight: bold;
  color: #303133;
  line-height: 1;
  margin-bottom: 4px;
}

.stat-label {
  font-size: 14px;
  color: #909399;
}

/* 内容卡片 */
.content-card {
  margin-bottom: 20px;
}

.card-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
}

.card-title {
  display: flex;
  align-items: center;
  gap: 8px;
  font-size: 16px;
  font-weight: 600;
  margin: 0;
  color: #303133;
}

/* 空状态 */
.empty-state {
  text-align: center;
  padding: 40px 20px;
  color: #909399;
}

.empty-state p {
  margin: 16px 0 20px 0;
  font-size: 14px;
}

/* 题目列表 */
.question-list {
  display: flex;
  flex-direction: column;
  gap: 12px;
}

.question-item {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 16px;
  background: #fafafa;
  border-radius: 8px;
  cursor: pointer;
  transition: all 0.2s ease;
}

.question-item:hover {
  background: #f0f2f5;
  transform: translateX(4px);
}

.question-info {
  flex: 1;
}

.question-title {
  font-size: 14px;
  font-weight: 500;
  color: #303133;
  margin: 0 0 8px 0;
}

.question-meta {
  display: flex;
  align-items: center;
  gap: 8px;
  font-size: 12px;
}

.question-category {
  color: #909399;
}

.question-score {
  font-weight: 500;
}

/* 活动时间线 */
.activity-timeline {
  padding: 0;
}

.activity-item {
  margin-bottom: 8px;
}

.activity-content {
  display: flex;
  align-items: center;
  gap: 8px;
  font-size: 14px;
}

.activity-action {
  color: #606266;
}

.activity-target {
  font-weight: 500;
  color: #303133;
}

/* 进度内容 */
.progress-content {
  display: flex;
  flex-direction: column;
  gap: 20px;
}

.progress-item {
  display: flex;
  flex-direction: column;
  gap: 8px;
}

.progress-info {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.progress-label {
  font-size: 14px;
  color: #606266;
}

.progress-value {
  font-size: 14px;
  font-weight: 500;
  color: #303133;
}

/* 快速操作 */
.quick-actions {
  display: flex;
  flex-direction: column;
  gap: 12px;
}

.action-button {
  justify-content: flex-start;
  height: 40px;
}

/* 提示卡片 */
.tip-card {
  background: linear-gradient(135deg, #ffeaa7, #fab1a0);
}

.tip-content {
  color: #2d3436;
}

.tip-text {
  font-size: 14px;
  line-height: 1.6;
  margin: 0;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .welcome-content {
    flex-direction: column;
    align-items: flex-start;
    gap: 16px;
  }

  .welcome-action {
    align-self: stretch;
  }

  .stats-grid {
    grid-template-columns: repeat(2, 1fr);
    gap: 12px;
  }

  .stat-number {
    font-size: 24px;
  }

  .question-item {
    flex-direction: column;
    align-items: flex-start;
    gap: 12px;
  }

  .question-item .el-button {
    align-self: stretch;
  }
}

@media (max-width: 480px) {
  .stats-grid {
    grid-template-columns: 1fr;
  }

  .welcome-title {
    font-size: 20px;
  }
}
</style>