import { defineStore } from 'pinia'
import { authApi } from '@/api'
import { ElMessage } from 'element-plus'
import Logger from '@/utils/logger'

export const useAuthStore = defineStore('auth', {
  state: () => ({
    user: JSON.parse(localStorage.getItem('user')) || null,
    token: localStorage.getItem('token') || '',
    tokenExpireTime: localStorage.getItem('tokenExpireTime') || null,
    isLoggedIn: false
  }),

  getters: {
    // 检查是否已登录且token未过期
    checkLoginStatus: (state) => {
      if (!state.token || !state.user) return false
      
      // 检查token是否过期
      if (state.tokenExpireTime) {
        const now = Date.now()
        const expireTime = parseInt(state.tokenExpireTime)
        if (now >= expireTime) {
          Logger.warn('Token已过期')
          return false
        }
      }
      
      return true
    },
    
    // 获取token剩余时间（分钟）
    tokenRemainingTime: (state) => {
      if (!state.tokenExpireTime) return null
      const remaining = parseInt(state.tokenExpireTime) - Date.now()
      return remaining > 0 ? Math.floor(remaining / 60000) : 0
    }
  },

  actions: {
    // 登录
    async login(loginForm) {
      try {
        const response = await authApi.login(loginForm)
        Logger.api('登录响应：', response)
        
        // 后端返回格式：{ code: 200, message: "登录成功", data: { token, username, expiresIn }, success: true }
        if (response.success && response.data) {
          this.token = response.data.token
          this.user = { 
            username: response.data.username,
            expiresIn: response.data.expiresIn 
          }
          
          // 计算token过期时间
          const expireTime = Date.now() + (response.data.expiresIn * 1000)
          this.tokenExpireTime = expireTime.toString()
          this.isLoggedIn = true

          // 保存到本地存储
          localStorage.setItem('token', this.token)
          localStorage.setItem('user', JSON.stringify(this.user))
          localStorage.setItem('tokenExpireTime', this.tokenExpireTime)
          
          ElMessage.success('登录成功')
          return { success: true }
        } else {
          ElMessage.error(response.message || '登录失败')
          return { success: false, message: response.message }
        }
      } catch (error) {
        Logger.error('登录错误：', error)
        ElMessage.error('登录失败，请检查网络连接')
        return { success: false, message: '网络错误' }
      }
    },

    // 刷新Token
    async refreshToken() {
      try {
        // 使用现有token调用刷新接口
        const response = await authApi.refreshToken()
        if (response.success && response.data) {
          const newToken = response.data
          this.token = newToken
          
          // 更新到本地存储
          localStorage.setItem('token', newToken)
          
          Logger.info('Token刷新成功')
          return newToken
        }
        return null
      } catch (error) {
        Logger.error('Token刷新失败:', error)
        return null
      }
    },

    // 登出
    async logout() {
      try {
        await authApi.logout()
      } catch (error) {
        Logger.error('登出API调用失败:', error)
      } finally {
        this.clearAuth()
        ElMessage.success('已退出登录')
      }
    },

    // 修改密码
    async changePassword(passwordData) {
      try {
        const response = await authApi.changePassword(passwordData)
        if (response.success) {
          ElMessage.success('密码修改成功')
          return { success: true }
        }
        return { success: false, message: response.message }
      } catch (error) {
        Logger.error('修改密码失败:', error)
        ElMessage.error('修改密码失败')
        return { success: false, message: '网络错误' }
      }
    },
    },

    // 初始化认证状态
    initAuth() {
      const token = localStorage.getItem('token')
      const user = localStorage.getItem('user')
      const tokenExpireTime = localStorage.getItem('tokenExpireTime')
      
      if (token && user) {
        this.token = token
        this.user = JSON.parse(user)
        this.tokenExpireTime = tokenExpireTime
        
        // 检查token是否过期
        if (tokenExpireTime) {
          const now = Date.now()
          const expireTime = parseInt(tokenExpireTime)
          if (now >= expireTime) {
            Logger.warn('Token已过期，清理本地存储')
            this.clearAuth()
            return
          }
        }
        
        this.isLoggedIn = true
        Logger.info('认证状态初始化成功')
      } else {
        this.clearAuth()
      }
    },
    
    // 清理认证信息（内部方法）
    clearAuth() {
      this.user = null
      this.token = ''
      this.tokenExpireTime = null
      this.isLoggedIn = false
      
      localStorage.removeItem('token')
      localStorage.removeItem('user')
      localStorage.removeItem('tokenExpireTime')
    }
  }
})
