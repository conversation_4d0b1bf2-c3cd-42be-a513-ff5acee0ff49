# 面试题库管理系统 - 前后端接口文档

## 概述

**基础URL**: `http://localhost:8080/api`  
**认证方式**: Bearer Token  
**内容类型**: `application/json`

## 全局响应格式

### 成功响应
```json
{
  "code": 200,
  "message": "操作成功",
  "data": {}, // 具体数据
  "success": true
}
```

### 错误响应
```json
{
  "code": 400/401/403/404/500,
  "message": "错误描述",
  "data": null,
  "success": false
}
```

## 认证模块 (/auth)

### 1. 用户登录
**POST** `/auth/login`

**请求体**:
```json
{
  "username": "string",
  "password": "string"
}
```

**成功响应**:
```json
{
  "code": 200,
  "message": "登录成功",
  "data": {
    "token": "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9...",
    "username": "admin",
    "expiresIn": 7200
  },
  "success": true
}
```

**错误响应**:
- `400`: 用户名或密码错误
- `401`: 账户被禁用

### 2. 刷新Token
**POST** `/auth/refresh`

**请求头**: `Authorization: Bearer {token}`

**成功响应**:
```json
{
  "code": 200,
  "message": "Token刷新成功",
  "data": "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9...",
  "success": true
}
```

**错误响应**:
- `401`: Token无效或已过期

### 3. 用户登出
**POST** `/auth/logout`

**请求头**: `Authorization: Bearer {token}`

**成功响应**:
```json
{
  "code": 200,
  "message": "登出成功",
  "data": null,
  "success": true
}
```

### 4. 修改密码
**POST** `/auth/change-password`

**请求头**: `Authorization: Bearer {token}`

**请求参数**:
```
oldPassword: string (当前密码)
newPassword: string (新密码)
```

**成功响应**:
```json
{
  "code": 200,
  "message": "密码修改成功",
  "data": null,
  "success": true
}
```

**错误响应**:
- `400`: 原密码错误
- `401`: Token无效

## 题目管理模块 (/questions)

### 1. 获取题目列表
**GET** `/questions`

**查询参数**:
```
page: number (页码，默认0)
pageSize: number (每页数量，默认10) 
categoryId: number (分类ID筛选)
difficulty: number (难度筛选: 1-简单/2-中等/3-困难)
keyword: string (关键词搜索)
```

**成功响应**:
```json
{
  "code": 200,
  "message": "获取成功",
  "data": {
    "questions": [
      {
        "id": 1,
        "title": "JavaScript闭包的概念",
        "content": "请解释JavaScript中闭包的概念...",
        "categoryId": 1,
        "difficulty": 2,
        "tags": "闭包,JavaScript,基础",
        "company": "腾讯",
        "answer": "闭包是指...",
        "createdAt": "2024-01-01T00:00:00Z",
        "updatedAt": "2024-01-01T00:00:00Z"
      }
    ],
    "total": 100,
    "page": 1,
    "pageSize": 10
  },
  "success": true
}
```

### 2. 获取题目详情
**GET** `/questions/{id}`

**路径参数**: `id` - 题目ID

**成功响应**:
```json
{
  "code": 200,
  "message": "获取成功",
  "data": {
    "id": 1,
    "title": "JavaScript闭包的概念",
    "content": "请解释JavaScript中闭包的概念...",
    "categoryId": 1,
    "difficulty": 2,
    "tags": "闭包,JavaScript,基础",
    "company": "腾讯",
    "answer": "闭包是指...",
    "createdAt": "2024-01-01T00:00:00Z",
    "updatedAt": "2024-01-01T00:00:00Z"
  },
  "success": true
}
```

### 3. 创建题目
**POST** `/questions`

**请求头**: `Authorization: Bearer {token}`

**请求体**:
```json
{
  "title": "string",
  "content": "string",
  "categoryId": "number",
  "difficulty": 1|2|3,
  "tags": "string",
  "company": "string",
  "answer": "string"
}
```

**成功响应**:
```json
{
  "code": 200,
  "message": "题目创建成功",
  "data": {
    "id": 123,
    "title": "新题目标题",
    // ... 其他字段
  },
  "success": true
}
```

### 4. 更新题目
**PUT** `/questions/{id}`

**请求头**: `Authorization: Bearer {token}`  
**路径参数**: `id` - 题目ID

**请求体**: 同创建题目

**成功响应**:
```json
{
  "code": 200,
  "message": "题目更新成功",
  "data": {
    "id": 123,
    // ... 更新后的题目数据
  },
  "success": true
}
```

### 5. 删除题目
**DELETE** `/questions/{id}`

**请求头**: `Authorization: Bearer {token}`  
**路径参数**: `id` - 题目ID

**成功响应**:
```json
{
  "code": 200,
  "message": "题目删除成功",
  "data": null,
  "success": true
}
```

### 6. 获取分类列表
**GET** `/questions/categories`

**成功响应**:
```json
{
  "code": 200,
  "message": "获取成功",
  "data": [
    {
      "id": 1,
      "name": "JavaScript",
      "questionCount": 25
    },
    {
      "id": 2,
      "name": "Vue.js",
      "questionCount": 18
    },
    {
      "id": 3,
      "name": "Node.js", 
      "questionCount": 12
    }
  ],
  "success": true
}
```

### 7. 获取随机题目
**GET** `/questions/random`

**查询参数**:
```
categoryId: number (可选，按分类筛选)
difficulty: number (可选，按难度筛选)
excludeIds: string (可选，排除的题目ID，逗号分隔)
```

**成功响应**:
```json
{
  "code": 200,
  "message": "获取成功",
  "data": {
    "id": 1,
    "title": "JavaScript闭包的概念",
    "content": "请解释JavaScript中闭包的概念...",
    "categoryId": 1,
    "difficulty": 2,
    "tags": "闭包,JavaScript,基础",
    "company": "腾讯",
    "answer": "闭包是指...",
    "createdAt": "2024-01-01T00:00:00Z"
  },
  "success": true
}
```

### 8. 获取待复习题目
**GET** `/questions/review`

**查询参数**:
```
limit: number (获取数量，默认10)
```

**成功响应**:
```json
{
  "code": 200,
  "message": "获取成功",
  "data": [
    {
      "id": 1,
      "title": "JavaScript闭包的概念",
      "content": "请解释JavaScript中闭包的概念...",
      "categoryId": 1,
      "difficulty": 2,
      "tags": "闭包,JavaScript,基础",
      "company": "腾讯"
    }
  ],
  "success": true
}
```

### 9. 批量删除题目
**DELETE** `/questions`

**请求头**: `Authorization: Bearer {token}`

**请求体**:
```json
[1, 2, 3, 4]  // 题目ID数组
```

**成功响应**:
```json
{
  "code": 200,
  "message": "题目批量删除成功",
  "data": null,
  "success": true
}
```

### 10. 根据分类获取题目
**GET** `/questions/category/{categoryId}`

**路径参数**: `categoryId` - 分类ID
**查询参数**:
```
page: number (页码，默认0)
pageSize: number (每页数量，默认10)
```

**成功响应**: 同获取题目列表

### 11. 根据难度获取题目  
**GET** `/questions/difficulty/{difficulty}`

**路径参数**: `difficulty` - 难度等级(1-简单/2-中等/3-困难)
**查询参数**:
```
page: number (页码，默认0) 
pageSize: number (每页数量，默认10)
```

**成功响应**: 同获取题目列表

### 12. 搜索题目
**GET** `/questions/search`

**查询参数**:
```
keyword: string (关键词，必填)
page: number (页码，默认0)
pageSize: number (每页数量，默认10)
```

**成功响应**: 同获取题目列表

## 分类管理模块 (/categories)

### 1. 获取所有分类
**GET** `/categories`

**成功响应**:
```json
{
  "code": 200,
  "message": "获取成功",
  "data": [
    {
      "id": 1,
      "name": "JavaScript",
      "parentId": null,
      "description": "JavaScript相关题目",
      "createTime": "2024-01-01T00:00:00Z"
    }
  ],
  "success": true
}
```

### 2. 获取顶级分类
**GET** `/categories/top`

**成功响应**: 同获取所有分类

### 3. 获取子分类
**GET** `/categories/{parentId}/children`

**路径参数**: `parentId` - 父分类ID

**成功响应**: 同获取所有分类

### 4. 获取分类详情
**GET** `/categories/{id}`

**路径参数**: `id` - 分类ID

**成功响应**:
```json
{
  "code": 200,
  "message": "获取成功",
  "data": {
    "id": 1,
    "name": "JavaScript",
    "parentId": null,
    "description": "JavaScript相关题目",
    "createTime": "2024-01-01T00:00:00Z"
  },
  "success": true
}
```

### 5. 创建分类
**POST** `/categories`

**请求头**: `Authorization: Bearer {token}`

**请求体**:
```json
{
  "name": "string",
  "parentId": "number (可选)",
  "description": "string (可选)"
}
```

**成功响应**:
```json
{
  "code": 200,
  "message": "分类创建成功",
  "data": null,
  "success": true
}
```

### 6. 更新分类
**PUT** `/categories/{id}`

**请求头**: `Authorization: Bearer {token}`
**路径参数**: `id` - 分类ID
**请求体**: 同创建分类

**成功响应**:
```json
{
  "code": 200,
  "message": "分类更新成功",
  "data": null,
  "success": true
}
```

### 7. 删除分类
**DELETE** `/categories/{id}`

**请求头**: `Authorization: Bearer {token}`
**路径参数**: `id` - 分类ID

**成功响应**:
```json
{
  "code": 200,
  "message": "分类删除成功",
  "data": null,
  "success": true
}
```

### 8. 获取分类树
**GET** `/categories/tree`

**成功响应**:
```json
{
  "code": 200,
  "message": "获取成功",
  "data": [
    {
      "id": 1,
      "name": "JavaScript",
      "parentId": null,
      "description": "JavaScript相关题目",
      "sortOrder": 1,
      "createTime": "2024-01-01T00:00:00Z",
      "questionCount": 25
    },
    {
      "id": 2,
      "name": "Vue.js",  
      "parentId": 1,
      "description": "Vue.js框架相关",
      "sortOrder": 2,
      "createTime": "2024-01-01T00:00:00Z",
      "questionCount": 18
    }
  ],
  "success": true
}
```

## 复习记录模块 (/reviews)

### 1. 提交复习记录
**POST** `/reviews`

**请求头**: `Authorization: Bearer {token}`

**请求体**:
```json
{
  "questionId": 1,
  "masteryScore": 85, // 掌握程度评分(0-100)
  "timeSpent": 120, // 用时（秒）
  "notes": "个人笔记（可选）"
}
```

**成功响应**:
```json
{
  "code": 200,
  "message": "复习记录提交成功",
  "data": {
    "id": 1,
    "questionId": 1,
    "masteryScore": 85,
    "timeSpent": 120,
    "notes": "需要再复习",
    "reviewTime": "2024-01-01T10:30:00Z"
  },
  "success": true
}
```

### 2. 获取最近复习记录
**GET** `/reviews/recent`

**请求头**: `Authorization: Bearer {token}`

**查询参数**:
```
limit: number (获取数量，默认10)
days: number (最近几天的记录，默认7)
```

**成功响应**:
```json
{
  "code": 200,
  "message": "获取成功",
  "data": [
    {
      "id": 1,
      "questionId": 1,
      "questionTitle": "JavaScript闭包的概念",
      "categoryId": 1,
      "masteryScore": 85,
      "timeSpent": 120,
      "reviewTime": "2024-01-01T10:30:00Z"
    }
  ],
  "success": true
}
```

### 3. 获取题目的复习历史
**GET** `/reviews/question/{questionId}`

**路径参数**: `questionId` - 题目ID

**成功响应**:
```json
{
  "code": 200,
  "message": "获取成功",
  "data": [
    {
      "id": 1,
      "questionId": 1,
      "masteryScore": 85,
      "timeSpent": 120,
      "notes": "需要再复习",
      "reviewTime": "2024-01-01T10:30:00Z"
    }
  ],
  "success": true
}
```

### 4. 获取需要复习的记录
**GET** `/reviews/needed`

**成功响应**: 同获取最近复习记录

### 5. 获取推荐复习的题目
**GET** `/reviews/recommended`

**查询参数**:
```
limit: number (获取数量，默认10)
```

**成功响应**:
```json
{
  "code": 200,
  "message": "获取成功",
  "data": [
    {
      "id": 1,
      "title": "JavaScript闭包的概念",
      "content": "请解释JavaScript中闭包的概念...",
      "categoryId": 1,
      "difficulty": 2,
      "tags": "闭包,JavaScript,基础"
    }
  ],
  "success": true
}
```

## 统计分析模块 (/stats)

### 1. 获取总体统计
**GET** `/stats`

**请求头**: `Authorization: Bearer {token}`

**成功响应**:
```json
{
  "code": 200,
  "message": "获取成功",
  "data": {
    "totalQuestions": 250,
    "totalReviews": 1200,
    "averageScore": 85.5,
    "studyDays": 45,
    "todayReviews": 8,
    "weeklyProgress": 78.5
  },
  "success": true
}
```

### 2. 获取复习统计
**GET** `/stats/reviews`

**请求头**: `Authorization: Bearer {token}`

**查询参数**:
```
period: string (统计周期: week|month|year，默认week)
```

**成功响应**:
```json
{
  "code": 200,
  "message": "获取成功",
  "data": {
    "totalReviews": 150,
    "averageTimeSpent": 95,
    "averageScore": 85.5,
    "masteryDistribution": {
      "high": 60,    // 80-100分
      "medium": 70,  // 60-80分  
      "low": 20      // 0-60分
    },
    "categoryStats": {
      "JavaScript": 45,
      "Vue.js": 30,
      "Node.js": 25
    },
    "dailyReviewCounts": [
      { "date": "2024-01-01", "count": 5, "avgScore": 85.2 },
      { "date": "2024-01-02", "count": 8, "avgScore": 78.9 }
    ]
  },
  "success": true
}
```

### 3. 获取分类统计
**GET** `/stats/categories`

**请求头**: `Authorization: Bearer {token}`

**成功响应**:
```json
{
  "code": 200,
  "message": "获取成功", 
  "data": [
    {
      "categoryId": 1,
      "categoryName": "JavaScript",
      "questionCount": 45,
      "reviewCount": 180,
      "averageScore": 88.5,
      "masteryRate": 76.8
    }
  ],
  "success": true
}
```

### 4. 获取难度统计
**GET** `/stats/difficulty`

**请求头**: `Authorization: Bearer {token}`

**成功响应**:
```json
{
  "code": 200,
  "message": "获取成功",
  "data": {
    "easy": {
      "count": 80,
      "averageScore": 92.1,
      "completionRate": 95.5
    },
    "medium": {
      "count": 120,
      "averageScore": 84.2,
      "completionRate": 78.3  
    },
    "hard": {
      "count": 50,
      "averageScore": 71.8,
      "completionRate": 62.1
    }
  },
  "success": true
}
```

### 5. 获取复习趋势
**GET** `/stats/trend`

**请求头**: `Authorization: Bearer {token}`

**查询参数**:
```
period: string (时间周期: 7days|30days|90days，默认30days)
```

**成功响应**:
```json
{
  "code": 200,
  "message": "获取成功",
  "data": {
    "reviewTrend": [
      { "date": "2024-01-01", "count": 5, "avgScore": 85.2 },
      { "date": "2024-01-02", "count": 8, "avgScore": 78.9 }
    ],
    "categoryTrend": {
      "JavaScript": [
        { "date": "2024-01-01", "count": 3 }
      ]
    }
  },
  "success": true
}
```

## 错误码说明

| 错误码 | 说明 |
|-------|------|
| 200 | 请求成功 |
| 400 | 请求参数错误 |
| 401 | 未授权/token无效 |
| 403 | 权限不足 |
| 404 | 资源不存在 |
| 500 | 服务器内部错误 |

## 重要实现说明

### 认证机制
1. 登录成功后返回JWT token
2. 所有需要认证的接口在请求头添加：`Authorization: Bearer {token}`
3. Token过期返回401，前端自动清除token并跳转登录页

### 前端数据处理
1. 前端期望所有响应都包含 `success` 字段判断请求状态
2. `response.data` 包含具体的业务数据
3. 分页数据需要包含 `total`, `page`, `pageSize` 字段

### 过滤和搜索
1. 题目列表支持按分类、难度、关键词筛选
2. 关键词搜索需要同时匹配标题和内容
3. 随机题目接口支持按分类、难度筛选，避免重复

**注意**：这份文档基于前端代码分析生成，请根据实际后端框架调整具体实现细节。