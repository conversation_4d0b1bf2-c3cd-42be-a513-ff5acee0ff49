/**
 * 统一日志工具
 * 开发环境显示详细日志，生产环境只显示错误
 */

const isDev = import.meta.env.MODE === 'development'

class Logger {
  static info(message, ...args) {
    if (isDev) {
      console.log(`[INFO] ${message}`, ...args)
    }
  }

  static warn(message, ...args) {
    if (isDev) {
      console.warn(`[WARN] ${message}`, ...args)
    }
  }

  static error(message, ...args) {
    console.error(`[ERROR] ${message}`, ...args)
  }

  static debug(message, ...args) {
    if (isDev) {
      console.debug(`[DEBUG] ${message}`, ...args)
    }
  }

  // 用于路由守卫等关键流程的日志
  static route(message, ...args) {
    if (isDev) {
      console.log(`[ROUTE] ${message}`, ...args)
    }
  }

  // 用于API请求的日志
  static api(message, ...args) {
    if (isDev) {
      console.log(`[API] ${message}`, ...args)
    }
  }
}

export default Logger