<template>
  <div class="login-container">
    <div class="login-wrapper">
      <!-- 背景装饰 -->
      <div class="background-decoration">
        <div class="decoration-circle circle-1"></div>
        <div class="decoration-circle circle-2"></div>
        <div class="decoration-circle circle-3"></div>
      </div>

      <!-- 登录卡片 -->
      <el-card class="login-card" shadow="always">
        <div class="login-header">
          <div class="logo">
            <el-icon :size="48" color="#409eff">
              <Reading />
            </el-icon>
          </div>
          <h1 class="login-title">面试复习系统</h1>
          <p class="login-subtitle">欢迎回来，开始你的学习之旅</p>
        </div>

        <el-form
          ref="loginFormRef"
          :model="loginForm"
          :rules="loginRules"
          class="login-form"
          size="large"
          @keyup.enter="handleLogin"
        >
          <el-form-item prop="username">
            <el-input
              v-model="loginForm.username"
              placeholder="请输入用户名"
              :prefix-icon="User"
              clearable
              autocomplete="username"
            />
          </el-form-item>

          <el-form-item prop="password">
            <el-input
              v-model="loginForm.password"
              type="password"
              placeholder="请输入密码"
              :prefix-icon="Lock"
              show-password
              clearable
              autocomplete="current-password"
            />
          </el-form-item>

          <el-form-item class="remember-item">
            <el-checkbox v-model="loginForm.rememberMe">
              记住我
            </el-checkbox>
          </el-form-item>

          <el-form-item class="login-button-item">
            <el-button
              type="primary"
              class="login-button"
              :loading="loading"
              @click="handleLogin"
              :disabled="loading"
            >
              <span v-if="!loading">立即登录</span>
              <span v-else>登录中...</span>
            </el-button>
          </el-form-item>
        </el-form>

        <div class="login-footer">
          <div class="demo-account">
            <p class="demo-title">演示账号</p>
            <div class="demo-buttons">
              <el-button 
                size="small" 
                type="info" 
                @click="fillDemoAccount"
                :disabled="loading"
                plain
              >
                使用演示账号
              </el-button>
            </div>
          </div>
        </div>
      </el-card>
    </div>
  </div>
</template>

<script setup>
import { ref, reactive, onMounted } from 'vue'
import { useRouter } from 'vue-router'
import { useAuthStore } from '@/stores/auth'
import Logger from '@/utils/logger'
import { ElMessage } from 'element-plus'
import { User, Lock, Reading } from '@element-plus/icons-vue'

// 响应式数据
const router = useRouter()
const authStore = useAuthStore()

const loginFormRef = ref()
const loading = ref(false)

const loginForm = reactive({
  username: '',
  password: '',
  rememberMe: false
})

// 表单验证规则
const loginRules = {
  username: [
    { required: true, message: '请输入用户名', trigger: 'blur' },
    { min: 3, max: 20, message: '用户名长度为 3-20 个字符', trigger: 'blur' }
  ],
  password: [
    { required: true, message: '请输入密码', trigger: 'blur' },
    { min: 6, max: 20, message: '密码长度为 6-20 个字符', trigger: 'blur' }
  ]
}

// 方法
const handleLogin = async () => {
  if (!loginFormRef.value) return

  try {
    // 表单验证
    const valid = await loginFormRef.value.validate()
    if (!valid) return

    loading.value = true

    // 调用登录接口
    const result = await authStore.login({
      username: loginForm.username,
      password: loginForm.password,
      rememberMe: loginForm.rememberMe
    })

    if (result.success) {
      ElMessage.success('登录成功！')
      // 跳转到首页
      router.push('/dashboard')
    } else {
      ElMessage.error(result.message || '登录失败')
    }
  } catch (error) {
    Logger.error('登录错误:', error)
    ElMessage.error('登录过程中发生错误，请重试')
  } finally {
    loading.value = false
  }
}

const fillDemoAccount = () => {
  loginForm.username = 'demo'
  loginForm.password = 'demo123'
  ElMessage.info('已填入演示账号信息')
}

// 生命周期
onMounted(() => {
  // 如果已经登录，跳转到首页
  if (authStore.checkLoginStatus) {
    router.push('/dashboard')
  }

  // 自动填充记住的用户名
  const rememberedUsername = localStorage.getItem('rememberedUsername')
  if (rememberedUsername) {
    loginForm.username = rememberedUsername
    loginForm.rememberMe = true
  }
})
</script>

<style scoped>
.login-container {
  min-height: 100vh;
  display: flex;
  align-items: center;
  justify-content: center;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  padding: 20px;
  position: relative;
  overflow: hidden;
}

.login-wrapper {
  position: relative;
  z-index: 10;
}

/* 背景装饰 */
.background-decoration {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  overflow: hidden;
  pointer-events: none;
}

.decoration-circle {
  position: absolute;
  border-radius: 50%;
  background: rgba(255, 255, 255, 0.1);
  animation: float 6s ease-in-out infinite;
}

.circle-1 {
  width: 100px;
  height: 100px;
  top: 10%;
  left: 10%;
  animation-delay: 0s;
}

.circle-2 {
  width: 150px;
  height: 150px;
  top: 70%;
  right: 10%;
  animation-delay: 2s;
}

.circle-3 {
  width: 80px;
  height: 80px;
  top: 50%;
  left: 80%;
  animation-delay: 4s;
}

@keyframes float {
  0%, 100% {
    transform: translateY(0px);
  }
  50% {
    transform: translateY(-20px);
  }
}

/* 登录卡片 */
.login-card {
  width: 100%;
  max-width: 400px;
  border-radius: 16px;
  overflow: hidden;
  backdrop-filter: blur(10px);
  background: rgba(255, 255, 255, 0.95);
}

.login-card :deep(.el-card__body) {
  padding: 40px 32px 32px;
}

/* 登录头部 */
.login-header {
  text-align: center;
  margin-bottom: 32px;
}

.logo {
  margin-bottom: 16px;
}

.login-title {
  font-size: 28px;
  font-weight: bold;
  color: #303133;
  margin: 0 0 8px 0;
  background: linear-gradient(45deg, #409eff, #67c23a);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
}

.login-subtitle {
  font-size: 14px;
  color: #909399;
  margin: 0;
}

/* 登录表单 */
.login-form {
  margin-top: 24px;
}

.login-form .el-form-item {
  margin-bottom: 20px;
}

.login-form .el-input {
  height: 48px;
}

.login-form .el-input__wrapper {
  border-radius: 8px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.08);
  transition: all 0.3s ease;
}

.login-form .el-input__wrapper:hover {
  box-shadow: 0 4px 12px rgba(64, 158, 255, 0.2);
}

.login-form .el-input.is-focus .el-input__wrapper {
  box-shadow: 0 4px 12px rgba(64, 158, 255, 0.3);
}

.remember-item {
  margin-bottom: 24px;
}

.remember-item .el-form-item__content {
  justify-content: flex-start;
}

.login-button-item {
  margin-bottom: 0;
}

.login-button {
  width: 100%;
  height: 48px;
  font-size: 16px;
  font-weight: 600;
  border-radius: 8px;
  background: linear-gradient(45deg, #409eff, #66b1ff);
  border: none;
  transition: all 0.3s ease;
}

.login-button:hover {
  transform: translateY(-2px);
  box-shadow: 0 8px 20px rgba(64, 158, 255, 0.4);
}

.login-button:active {
  transform: translateY(0);
}

/* 登录页脚 */
.login-footer {
  margin-top: 24px;
  text-align: center;
}

.demo-account {
  padding: 16px;
  background: #f8f9fa;
  border-radius: 8px;
}

.demo-title {
  font-size: 12px;
  color: #909399;
  margin: 0 0 8px 0;
}

.demo-buttons {
  display: flex;
  justify-content: center;
}

/* 响应式设计 */
@media (max-width: 480px) {
  .login-container {
    padding: 12px;
  }

  .login-card :deep(.el-card__body) {
    padding: 24px 20px 20px;
  }

  .login-title {
    font-size: 24px;
  }

  .login-form .el-input {
    height: 44px;
  }

  .login-button {
    height: 44px;
    font-size: 15px;
  }
}

/* 加载状态 */
.login-button.is-loading {
  opacity: 0.8;
}

/* 输入框聚焦效果 */
.login-form .el-input__inner:focus {
  border-color: #409eff;
}

/* 错误状态样式 */
.login-form .el-form-item.is-error .el-input__wrapper {
  box-shadow: 0 2px 8px rgba(245, 108, 108, 0.2);
}

/* 复选框样式 */
.login-form .el-checkbox {
  color: #606266;
  font-size: 14px;
}

.login-form .el-checkbox__input.is-checked .el-checkbox__inner {
  background-color: #409eff;
  border-color: #409eff;
}
</style>