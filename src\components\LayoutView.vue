<template>
  <el-container class="layout-container">
    <!-- 移动端头部 -->
    <el-header v-if="isMobile" class="mobile-header">
      <div class="mobile-header-content">
        <el-button 
          class="menu-toggle" 
          type="text" 
          @click="handleDrawerToggle"
          :icon="Menu"
        />
        <h1 class="app-title">面试复习</h1>
        <el-dropdown @command="handleCommand">
          <el-button class="user-avatar" type="text" :icon="User" />
          <template #dropdown>
            <el-dropdown-menu>
              <el-dropdown-item command="logout">退出登录</el-dropdown-item>
            </el-dropdown-menu>
          </template>
        </el-dropdown>
      </div>
    </el-header>

    <el-container>
      <!-- 桌面端侧边栏 -->
      <el-aside v-if="!isMobile" width="250px" class="app-sidebar">
        <div class="sidebar-header">
          <h1 class="app-title">📚 面试复习</h1>
        </div>
        <el-menu
          :default-active="$route.path"
          router
          class="sidebar-menu"
          :collapse="false"
        >
          <el-menu-item 
            v-for="item in menuItems" 
            :key="item.path"
            :index="item.path"
          >
            <el-icon><component :is="item.icon" /></el-icon>
            <template #title>{{ item.title }}</template>
          </el-menu-item>
        </el-menu>
        
        <div class="sidebar-footer">
          <el-dropdown @command="handleCommand" placement="top-start">
            <div class="user-info">
              <el-avatar :size="32" :icon="User" />
              <div class="user-details">
                <div class="username">{{ userStore.user?.username || '用户' }}</div>
                <div class="user-role">学习者</div>
              </div>
            </div>
            <template #dropdown>
              <el-dropdown-menu>
                <el-dropdown-item command="logout">退出登录</el-dropdown-item>
              </el-dropdown-menu>
            </template>
          </el-dropdown>
        </div>
      </el-aside>

      <!-- 移动端抽屉菜单 -->
      <el-drawer
        v-model="drawerVisible"
        direction="ltr"
        :with-header="false"
        size="280px"
        class="mobile-drawer"
      >
        <div class="drawer-content">
          <div class="drawer-header">
            <h1 class="app-title">📚 面试复习</h1>
          </div>
          
          <el-menu
            :default-active="$route.path"
            router
            class="drawer-menu"
            @select="handleMenuSelect"
          >
            <el-menu-item 
              v-for="item in menuItems" 
              :key="item.path"
              :index="item.path"
            >
              <el-icon><component :is="item.icon" /></el-icon>
              <template #title>{{ item.title }}</template>
            </el-menu-item>
          </el-menu>

          <div class="drawer-footer">
            <div class="user-info">
              <el-avatar :size="40" :icon="User" />
              <div class="user-details">
                <div class="username">{{ userStore.user?.username || '用户' }}</div>
                <div class="user-role">学习者</div>
              </div>
            </div>
            <el-button 
              type="danger" 
              size="small" 
              @click="handleLogout"
              :icon="SwitchButton"
            >
              退出登录
            </el-button>
          </div>
        </div>
      </el-drawer>

      <!-- 主内容区域 -->
      <el-main class="app-main">
        <div class="main-content">
          <!-- 桌面端头部 -->
          <div v-if="!isMobile" class="desktop-header">
            <div class="breadcrumb">
              <h2 class="page-title">{{ currentPageTitle }}</h2>
            </div>
          </div>

          <!-- 页面内容 -->
          <div class="page-content fade-in">
            <router-view />
          </div>
        </div>
      </el-main>
    </el-container>
  </el-container>
</template>

<script setup>
import { ref, computed, onMounted, onBeforeUnmount } from 'vue'
import { useRouter, useRoute } from 'vue-router'
import { useAuthStore } from '@/stores/auth'
import { ElMessageBox } from 'element-plus'
import { 
  Menu, 
  User, 
  SwitchButton,
  House,
  Management,
  Reading,
  DataAnalysis
} from '@element-plus/icons-vue'

// 响应式数据
const router = useRouter()
const route = useRoute()
const userStore = useAuthStore()

const drawerVisible = ref(false)
const screenWidth = ref(window.innerWidth)

// 计算属性
const isMobile = computed(() => screenWidth.value < 768)

const currentPageTitle = computed(() => {
  return route.meta?.title || '面试复习系统'
})

// 菜单项配置
const menuItems = [
  { path: '/dashboard', title: '仪表板', icon: 'House' },
  { path: '/questions', title: '题库管理', icon: 'Management' },
  { path: '/review', title: '开始复习', icon: 'Reading' },
  { path: '/stats', title: '学习统计', icon: 'DataAnalysis' }
]

// 方法
const handleDrawerToggle = () => {
  drawerVisible.value = !drawerVisible.value
}

const handleMenuSelect = () => {
  // 移动端菜单选择后关闭抽屉
  if (isMobile.value) {
    drawerVisible.value = false
  }
}

const handleCommand = (command) => {
  if (command === 'logout') {
    handleLogout()
  }
}

const handleLogout = async () => {
  try {
    await ElMessageBox.confirm('确定要退出登录吗？', '提示', {
      confirmButtonText: '确定',
      cancelButtonText: '取消',
      type: 'warning'
    })
    
    await userStore.logout()
    router.push('/login')
  } catch (error) {
    // 用户取消操作
  }
}

const handleResize = () => {
  screenWidth.value = window.innerWidth
  // 桌面端时关闭抽屉
  if (!isMobile.value) {
    drawerVisible.value = false
  }
}

// 生命周期
onMounted(() => {
  window.addEventListener('resize', handleResize)
})

onBeforeUnmount(() => {
  window.removeEventListener('resize', handleResize)
})
</script>

<style scoped>
/* 布局容器 */
.layout-container {
  height: 100vh;
  background: #f5f7fa;
}

/* 移动端头部 */
.mobile-header {
  background: white;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  padding: 0;
  height: 60px;
  display: flex;
  align-items: center;
  z-index: 1000;
}

.mobile-header-content {
  display: flex;
  align-items: center;
  justify-content: space-between;
  width: 100%;
  padding: 0 16px;
}

.menu-toggle {
  font-size: 18px;
  padding: 8px;
}

.app-title {
  font-size: 18px;
  font-weight: 600;
  color: #303133;
  margin: 0;
  flex: 1;
  text-align: center;
}

.user-avatar {
  font-size: 18px;
  padding: 8px;
}

/* 桌面端侧边栏 */
.app-sidebar {
  background: white;
  box-shadow: 2px 0 8px rgba(0, 0, 0, 0.1);
  display: flex;
  flex-direction: column;
  height: 100vh;
}

.sidebar-header {
  padding: 24px 20px;
  border-bottom: 1px solid #e4e7ed;
}

.sidebar-header .app-title {
  font-size: 20px;
  font-weight: bold;
  color: #409eff;
  text-align: left;
}

.sidebar-menu {
  flex: 1;
  border: none;
  padding: 12px 8px;
}

.sidebar-menu .el-menu-item {
  border-radius: 8px;
  margin: 4px 0;
  height: 48px;
  line-height: 48px;
}

.sidebar-menu .el-menu-item:hover {
  background: #ecf5ff;
  color: #409eff;
}

.sidebar-menu .el-menu-item.is-active {
  background: linear-gradient(45deg, #409eff, #66b1ff);
  color: white;
}

.sidebar-footer {
  padding: 16px;
  border-top: 1px solid #e4e7ed;
}

/* 移动端抽屉 */
.mobile-drawer :deep(.el-drawer__body) {
  padding: 0;
}

.drawer-content {
  height: 100%;
  display: flex;
  flex-direction: column;
}

.drawer-header {
  padding: 20px;
  border-bottom: 1px solid #e4e7ed;
}

.drawer-menu {
  flex: 1;
  border: none;
  padding: 12px;
}

.drawer-menu .el-menu-item {
  border-radius: 8px;
  margin: 4px 0;
  height: 48px;
}

.drawer-footer {
  padding: 16px;
  border-top: 1px solid #e4e7ed;
  display: flex;
  flex-direction: column;
  gap: 12px;
}

/* 用户信息 */
.user-info {
  display: flex;
  align-items: center;
  gap: 12px;
  padding: 8px;
  cursor: pointer;
}

.user-details {
  flex: 1;
}

.username {
  font-size: 14px;
  font-weight: 500;
  color: #303133;
  line-height: 1.4;
}

.user-role {
  font-size: 12px;
  color: #909399;
  line-height: 1.2;
}

/* 主内容区域 */
.app-main {
  background: #f5f7fa;
  padding: 0;
  overflow-y: auto;
}

.main-content {
  height: 100%;
  display: flex;
  flex-direction: column;
}

.desktop-header {
  background: white;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.06);
  padding: 16px 24px;
  margin-bottom: 20px;
}

.breadcrumb {
  display: flex;
  align-items: center;
  justify-content: space-between;
}

.page-title {
  font-size: 24px;
  font-weight: 600;
  color: #303133;
  margin: 0;
}

.page-content {
  flex: 1;
  padding: 0 20px 20px;
  overflow-y: auto;
}

/* 移动端样式调整 */
@media (max-width: 768px) {
  .app-main {
    padding: 0;
  }

  .page-content {
    padding: 12px;
  }
}

/* 动画效果 */
.fade-in {
  animation: fadeIn 0.3s ease-in;
}

@keyframes fadeIn {
  from {
    opacity: 0;
    transform: translateY(10px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}
</style>