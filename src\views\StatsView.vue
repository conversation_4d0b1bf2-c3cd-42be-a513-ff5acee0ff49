<template>
  <div class="stats-container">
    <!-- 总体统计卡片 -->
    <div class="overview-section">
      <el-row :gutter="20">
        <el-col :xl="6" :lg="12" :md="12" :sm="24" :xs="24">
          <el-card class="stat-card total-card" shadow="hover">
            <div class="stat-content">
              <div class="stat-icon" style="background: linear-gradient(45deg, #409eff, #66b1ff)">
                <el-icon :size="28"><Management /></el-icon>
              </div>
              <div class="stat-info">
                <div class="stat-number">{{ overallStats.totalQuestions }}</div>
                <div class="stat-label">题目总数</div>
              </div>
            </div>
          </el-card>
        </el-col>

        <el-col :xl="6" :lg="12" :md="12" :sm="24" :xs="24">
          <el-card class="stat-card review-card" shadow="hover">
            <div class="stat-content">
              <div class="stat-icon" style="background: linear-gradient(45deg, #67c23a, #85ce61)">
                <el-icon :size="28"><Reading /></el-icon>
              </div>
              <div class="stat-info">
                <div class="stat-number">{{ overallStats.totalReviews }}</div>
                <div class="stat-label">总复习次数</div>
              </div>
            </div>
          </el-card>
        </el-col>

        <el-col :xl="6" :lg="12" :md="12" :sm="24" :xs="24">
          <el-card class="stat-card score-card" shadow="hover">
            <div class="stat-content">
              <div class="stat-icon" style="background: linear-gradient(45deg, #e6a23c, #f7ba2a)">
                <el-icon :size="28"><TrendCharts /></el-icon>
              </div>
              <div class="stat-info">
                <div class="stat-number">{{ overallStats.avgScore.toFixed(1) }}</div>
                <div class="stat-label">平均得分</div>
              </div>
            </div>
          </el-card>
        </el-col>

        <el-col :xl="6" :lg="12" :md="12" :sm="24" :xs="24">
          <el-card class="stat-card streak-card" shadow="hover">
            <div class="stat-content">
              <div class="stat-icon" style="background: linear-gradient(45deg, #f56c6c, #f89898)">
                <el-icon :size="28"><Trophy /></el-icon>
              </div>
              <div class="stat-info">
                <div class="stat-number">{{ overallStats.streak }}</div>
                <div class="stat-label">连续学习天数</div>
              </div>
            </div>
          </el-card>
        </el-col>
      </el-row>
    </div>

    <!-- 图表区域 -->
    <div class="charts-section">
      <el-row :gutter="20">
        <!-- 复习趋势图 -->
        <el-col :lg="16" :md="24" :sm="24" :xs="24">
          <el-card class="chart-card" shadow="never">
            <template #header>
              <div class="chart-header">
                <h3 class="chart-title">
                  <el-icon><DataLine /></el-icon>
                  复习趋势
                </h3>
                <div class="chart-controls">
                  <el-radio-group v-model="trendPeriod" @change="loadTrendData">
                    <el-radio-button label="7">最近7天</el-radio-button>
                    <el-radio-button label="30">最近30天</el-radio-button>
                  </el-radio-group>
                </div>
              </div>
            </template>
            
            <div class="chart-container">
              <canvas ref="trendChartRef" class="trend-chart"></canvas>
            </div>
          </el-card>
        </el-col>

        <!-- 得分分布 -->
        <el-col :lg="8" :md="24" :sm="24" :xs="24">
          <el-card class="chart-card" shadow="never">
            <template #header>
              <div class="chart-header">
                <h3 class="chart-title">
                  <el-icon><PieChart /></el-icon>
                  得分分布
                </h3>
              </div>
            </template>
            
            <div class="chart-container">
              <canvas ref="scoreChartRef" class="score-chart"></canvas>
            </div>

            <div class="score-legend">
              <div 
                v-for="(item, index) in scoreDistribution" 
                :key="index"
                class="legend-item"
              >
                <div 
                  class="legend-color" 
                  :style="{ backgroundColor: scoreColors[index] }"
                ></div>
                <span class="legend-text">{{ item.label }}: {{ item.count }}</span>
              </div>
            </div>
          </el-card>
        </el-col>
      </el-row>

      <el-row :gutter="20">
        <!-- 分类掌握情况 -->
        <el-col :lg="12" :md="24" :sm="24" :xs="24">
          <el-card class="chart-card" shadow="never">
            <template #header>
              <div class="chart-header">
                <h3 class="chart-title">
                  <el-icon><Grid /></el-icon>
                  分类掌握情况
                </h3>
              </div>
            </template>
            
            <div class="category-stats">
              <div 
                v-for="category in categoryStats" 
                :key="category.name"
                class="category-item"
              >
                <div class="category-info">
                  <span class="category-name">{{ category.name }}</span>
                  <span class="category-score">{{ category.avgScore.toFixed(1) }}分</span>
                </div>
                <el-progress 
                  :percentage="(category.avgScore / 5) * 100"
                  :color="getScoreColor(category.avgScore)"
                  :stroke-width="8"
                />
                <div class="category-details">
                  <span class="detail-text">{{ category.reviewCount }} 次复习</span>
                  <span class="detail-text">{{ category.questionCount }} 道题目</span>
                </div>
              </div>
            </div>
          </el-card>
        </el-col>

        <!-- 难度分析 -->
        <el-col :lg="12" :md="24" :sm="24" :xs="24">
          <el-card class="chart-card" shadow="never">
            <template #header>
              <div class="chart-header">
                <h3 class="chart-title">
                  <el-icon><DataAnalysis /></el-icon>
                  难度分析
                </h3>
              </div>
            </template>
            
            <div class="difficulty-stats">
              <div 
                v-for="difficulty in difficultyStats" 
                :key="difficulty.level"
                class="difficulty-item"
              >
                <div class="difficulty-header">
                  <div class="difficulty-info">
                    <el-tag 
                      :type="getDifficultyType(difficulty.level)" 
                      size="large"
                    >
                      {{ getDifficultyText(difficulty.level) }}
                    </el-tag>
                    <span class="difficulty-score">
                      平均 {{ difficulty.avgScore.toFixed(1) }} 分
                    </span>
                  </div>
                  <div class="difficulty-progress">
                    <el-progress 
                      type="circle" 
                      :percentage="(difficulty.avgScore / 5) * 100"
                      :color="getScoreColor(difficulty.avgScore)"
                      :width="60"
                    />
                  </div>
                </div>
                
                <div class="difficulty-details">
                  <div class="detail-row">
                    <span>题目数量：</span>
                    <span>{{ difficulty.questionCount }}</span>
                  </div>
                  <div class="detail-row">
                    <span>复习次数：</span>
                    <span>{{ difficulty.reviewCount }}</span>
                  </div>
                  <div class="detail-row">
                    <span>正确率：</span>
                    <span>{{ ((difficulty.correctCount / difficulty.reviewCount) * 100).toFixed(1) }}%</span>
                  </div>
                </div>
              </div>
            </div>
          </el-card>
        </el-col>
      </el-row>
    </div>

    <!-- 详细记录 -->
    <div class="records-section">
      <el-card class="records-card" shadow="never">
        <template #header>
          <div class="records-header">
            <h3 class="records-title">
              <el-icon><List /></el-icon>
              最近复习记录
            </h3>
            <div class="records-controls">
              <el-select 
                v-model="recordsFilter.category"
                placeholder="筛选分类"
                clearable
                size="small"
                style="width: 120px; margin-right: 8px;"
              >
                <el-option
                  v-for="category in categories"
                  :key="category"
                  :label="category"
                  :value="category"
                />
              </el-select>
              
              <el-select 
                v-model="recordsFilter.period"
                placeholder="时间范围"
                size="small"
                style="width: 120px;"
                @change="loadReviewRecords"
              >
                <el-option label="今天" value="today" />
                <el-option label="最近7天" value="week" />
                <el-option label="最近30天" value="month" />
              </el-select>
            </div>
          </div>
        </template>

        <el-table 
          :data="filteredRecords" 
          stripe 
          class="records-table"
          :default-sort="{ prop: 'createdAt', order: 'descending' }"
        >
          <el-table-column prop="questionTitle" label="题目" min-width="200">
            <template #default="scope">
              <div class="question-info">
                <div class="question-title">{{ scope.row.questionTitle }}</div>
                <div class="question-meta">
                  <el-tag 
                    :type="getDifficultyType(scope.row.difficulty)" 
                    size="small"
                  >
                    {{ getDifficultyText(scope.row.difficulty) }}
                  </el-tag>
                  <span class="question-category">{{ scope.row.category }}</span>
                </div>
              </div>
            </template>
          </el-table-column>

          <el-table-column prop="score" label="得分" width="100" align="center">
            <template #default="scope">
              <span :class="`score-badge score-${scope.row.score}`">
                {{ scope.row.score }}分
              </span>
            </template>
          </el-table-column>

          <el-table-column prop="duration" label="用时" width="100" align="center">
            <template #default="scope">
              <span class="duration-text">{{ formatDuration(scope.row.duration) }}</span>
            </template>
          </el-table-column>

          <el-table-column prop="createdAt" label="复习时间" width="160" align="center" sortable>
            <template #default="scope">
              <span class="time-text">{{ formatTime(scope.row.createdAt) }}</span>
            </template>
          </el-table-column>
        </el-table>

        <div class="pagination-wrapper">
          <el-pagination
            v-model:current-page="recordsPagination.current"
            v-model:page-size="recordsPagination.size"
            :page-sizes="[10, 20, 50]"
            :small="isMobile"
            :total="recordsPagination.total"
            layout="total, sizes, prev, pager, next"
            @current-change="loadReviewRecords"
            @size-change="loadReviewRecords"
          />
        </div>
      </el-card>
    </div>
  </div>
</template>

<script setup>
import { ref, reactive, computed, onMounted, onUnmounted, nextTick } from 'vue'
import { useReviewStore } from '@/stores/review'
import { useQuestionStore } from '@/stores/question'
import {
  Management, Reading, TrendCharts, Trophy, DataLine, PieChart,
  Grid, DataAnalysis, List
} from '@element-plus/icons-vue'

// Store
const reviewStore = useReviewStore()
const questionStore = useQuestionStore()

// 响应式数据
const loading = ref(false)
const isMobile = ref(window.innerWidth < 768)
const trendPeriod = ref('7')

const trendChartRef = ref()
const scoreChartRef = ref()

const recordsFilter = reactive({
  category: '',
  period: 'week'
})

const recordsPagination = reactive({
  current: 1,
  size: 20,
  total: 0
})

// 模拟数据
const overallStats = reactive({
  totalQuestions: 45,
  totalReviews: 128,
  avgScore: 3.6,
  streak: 12
})

const scoreColors = ['#f56c6c', '#e6a23c', '#409eff', '#67c23a', '#67c23a']

const trendData = ref([
  { date: '2024-01-15', reviews: 5, avgScore: 3.2 },
  { date: '2024-01-16', reviews: 8, avgScore: 3.8 },
  { date: '2024-01-17', reviews: 6, avgScore: 3.5 },
  { date: '2024-01-18', reviews: 10, avgScore: 4.1 },
  { date: '2024-01-19', reviews: 7, avgScore: 3.9 },
  { date: '2024-01-20', reviews: 9, avgScore: 4.2 },
  { date: '2024-01-21', reviews: 11, avgScore: 4.0 }
])

const scoreDistribution = ref([
  { label: '1分', count: 8 },
  { label: '2分', count: 15 },
  { label: '3分', count: 35 },
  { label: '4分', count: 42 },
  { label: '5分', count: 28 }
])

const categoryStats = ref([
  { name: 'JavaScript', avgScore: 4.2, reviewCount: 35, questionCount: 12 },
  { name: 'Vue.js', avgScore: 3.8, reviewCount: 28, questionCount: 10 },
  { name: 'CSS', avgScore: 4.0, reviewCount: 22, questionCount: 8 },
  { name: '算法', avgScore: 2.9, reviewCount: 18, questionCount: 6 },
  { name: '网络协议', avgScore: 3.5, reviewCount: 15, questionCount: 5 },
  { name: '数据库', avgScore: 3.2, reviewCount: 10, questionCount: 4 }
])

const difficultyStats = ref([
  { 
    level: 'easy', 
    avgScore: 4.3, 
    questionCount: 15, 
    reviewCount: 45, 
    correctCount: 38 
  },
  { 
    level: 'medium', 
    avgScore: 3.5, 
    questionCount: 20, 
    reviewCount: 58, 
    correctCount: 42 
  },
  { 
    level: 'hard', 
    avgScore: 2.8, 
    questionCount: 10, 
    reviewCount: 25, 
    correctCount: 15 
  }
])

const reviewRecords = ref([
  {
    id: 1,
    questionTitle: 'JavaScript闭包的原理和应用',
    category: 'JavaScript',
    difficulty: 'medium',
    score: 4,
    duration: 180000, // 3分钟
    createdAt: '2024-01-21T14:30:00Z'
  },
  {
    id: 2,
    questionTitle: 'Vue3 Composition API详解',
    category: 'Vue.js',
    difficulty: 'hard',
    score: 3,
    duration: 240000, // 4分钟
    createdAt: '2024-01-21T13:45:00Z'
  },
  // 更多记录...
])

// 计算属性
const categories = computed(() => {
  return [...new Set(reviewRecords.value.map(r => r.category))]
})

const filteredRecords = computed(() => {
  let filtered = reviewRecords.value
  
  if (recordsFilter.category) {
    filtered = filtered.filter(r => r.category === recordsFilter.category)
  }
  
  // 根据时间范围过滤
  const now = new Date()
  const filterDate = new Date()
  
  switch (recordsFilter.period) {
    case 'today':
      filterDate.setHours(0, 0, 0, 0)
      break
    case 'week':
      filterDate.setDate(now.getDate() - 7)
      break
    case 'month':
      filterDate.setDate(now.getDate() - 30)
      break
  }
  
  filtered = filtered.filter(r => new Date(r.createdAt) >= filterDate)
  
  recordsPagination.total = filtered.length
  
  const start = (recordsPagination.current - 1) * recordsPagination.size
  const end = start + recordsPagination.size
  
  return filtered.slice(start, end)
})

// 方法
const loadTrendData = async (days = trendPeriod.value) => {
  loading.value = true
  try {
    await reviewStore.fetchTrendData(Number(days))
    await nextTick()
    drawTrendChart()
  } finally {
    loading.value = false
  }
}

const loadReviewRecords = async () => {
  loading.value = true
  try {
    await reviewStore.fetchReviewHistory({
      page: recordsPagination.current,
      size: recordsPagination.size,
      period: recordsFilter.period
    })
  } finally {
    loading.value = false
  }
}

const drawTrendChart = () => {
  const canvas = trendChartRef.value
  if (!canvas) return

  const ctx = canvas.getContext('2d')
  const rect = canvas.getBoundingClientRect()
  
  // 设置画布大小
  canvas.width = rect.width * window.devicePixelRatio
  canvas.height = rect.height * window.devicePixelRatio
  ctx.scale(window.devicePixelRatio, window.devicePixelRatio)

  // 清除画布
  ctx.clearRect(0, 0, rect.width, rect.height)

  if (trendData.value.length === 0) return

  const padding = { top: 20, right: 20, bottom: 60, left: 60 }
  const chartWidth = rect.width - padding.left - padding.right
  const chartHeight = rect.height - padding.top - padding.bottom

  // 绘制趋势线
  const maxReviews = Math.max(...trendData.value.map(d => d.reviews))
  const minReviews = Math.min(...trendData.value.map(d => d.reviews))
  
  // 绘制网格线
  ctx.strokeStyle = '#e4e7ed'
  ctx.lineWidth = 1
  
  // 垂直网格线
  for (let i = 0; i <= trendData.value.length - 1; i++) {
    const x = padding.left + (i * chartWidth) / (trendData.value.length - 1)
    ctx.beginPath()
    ctx.moveTo(x, padding.top)
    ctx.lineTo(x, padding.top + chartHeight)
    ctx.stroke()
  }
  
  // 水平网格线
  for (let i = 0; i <= 5; i++) {
    const y = padding.top + (i * chartHeight) / 5
    ctx.beginPath()
    ctx.moveTo(padding.left, y)
    ctx.lineTo(padding.left + chartWidth, y)
    ctx.stroke()
  }

  // 绘制复习次数线
  ctx.strokeStyle = '#409eff'
  ctx.lineWidth = 3
  ctx.beginPath()
  
  trendData.value.forEach((data, index) => {
    const x = padding.left + (index * chartWidth) / (trendData.value.length - 1)
    const y = padding.top + chartHeight - ((data.reviews - minReviews) / (maxReviews - minReviews)) * chartHeight
    
    if (index === 0) {
      ctx.moveTo(x, y)
    } else {
      ctx.lineTo(x, y)
    }
  })
  ctx.stroke()

  // 绘制数据点
  trendData.value.forEach((data, index) => {
    const x = padding.left + (index * chartWidth) / (trendData.value.length - 1)
    const y = padding.top + chartHeight - ((data.reviews - minReviews) / (maxReviews - minReviews)) * chartHeight
    
    ctx.fillStyle = '#409eff'
    ctx.beginPath()
    ctx.arc(x, y, 4, 0, 2 * Math.PI)
    ctx.fill()
  })

  // 绘制 X 轴标签
  ctx.fillStyle = '#606266'
  ctx.font = '12px sans-serif'
  ctx.textAlign = 'center'
  
  trendData.value.forEach((data, index) => {
    const x = padding.left + (index * chartWidth) / (trendData.value.length - 1)
    const date = new Date(data.date)
    const label = `${date.getMonth() + 1}/${date.getDate()}`
    ctx.fillText(label, x, padding.top + chartHeight + 20)
  })

  // 绘制 Y 轴标签
  ctx.textAlign = 'right'
  for (let i = 0; i <= 5; i++) {
    const value = minReviews + (i * (maxReviews - minReviews)) / 5
    const y = padding.top + chartHeight - (i * chartHeight) / 5
    ctx.fillText(Math.round(value).toString(), padding.left - 10, y + 4)
  }
}

const drawScoreChart = () => {
  const canvas = scoreChartRef.value
  if (!canvas) return

  const ctx = canvas.getContext('2d')
  const rect = canvas.getBoundingClientRect()
  
  canvas.width = rect.width * window.devicePixelRatio
  canvas.height = rect.height * window.devicePixelRatio
  ctx.scale(window.devicePixelRatio, window.devicePixelRatio)

  ctx.clearRect(0, 0, rect.width, rect.height)

  const centerX = rect.width / 2
  const centerY = rect.height / 2
  const radius = Math.min(centerX, centerY) - 20

  const total = scoreDistribution.value.reduce((sum, item) => sum + item.count, 0)
  let currentAngle = -Math.PI / 2

  scoreDistribution.value.forEach((item, index) => {
    const percentage = item.count / total
    const sliceAngle = percentage * 2 * Math.PI

    ctx.fillStyle = scoreColors[index]
    ctx.beginPath()
    ctx.arc(centerX, centerY, radius, currentAngle, currentAngle + sliceAngle)
    ctx.lineTo(centerX, centerY)
    ctx.fill()

    currentAngle += sliceAngle
  })
}

const getDifficultyType = (difficulty) => {
  const types = {
    easy: 'success',
    medium: 'warning',
    hard: 'danger'
  }
  return types[difficulty] || 'info'
}

const getDifficultyText = (difficulty) => {
  const texts = {
    easy: '简单',
    medium: '中等',
    hard: '困难'
  }
  return texts[difficulty] || '未知'
}

const getScoreColor = (score) => {
  if (score >= 4) return '#67c23a'
  if (score >= 3) return '#e6a23c'
  return '#f56c6c'
}

const formatDuration = (duration) => {
  const minutes = Math.floor(duration / 60000)
  const seconds = Math.floor((duration % 60000) / 1000)
  return `${minutes}:${seconds.toString().padStart(2, '0')}`
}

const formatTime = (timeString) => {
  const date = new Date(timeString)
  return date.toLocaleString('zh-CN', {
    month: '2-digit',
    day: '2-digit',
    hour: '2-digit',
    minute: '2-digit'
  })
}

const handleResize = () => {
  isMobile.value = window.innerWidth < 768
  // 重新绘制图表
  nextTick(() => {
    drawTrendChart()
    drawScoreChart()
  })
}

// 生命周期
onMounted(async () => {
  await Promise.all([
    loadTrendData(),
    loadReviewRecords(),
    reviewStore.fetchStats()
  ])

  await nextTick()
  drawTrendChart()
  drawScoreChart()

  window.addEventListener('resize', handleResize)
})

onUnmounted(() => {
  window.removeEventListener('resize', handleResize)
})
</script>

<style scoped>
.stats-container {
  padding: 0;
}

/* 概览部分 */
.overview-section {
  margin-bottom: 24px;
}

.stat-card {
  height: 120px;
  cursor: pointer;
  transition: all 0.3s ease;
}

.stat-card:hover {
  transform: translateY(-4px);
  box-shadow: 0 8px 30px rgba(0, 0, 0, 0.15);
}

.stat-content {
  display: flex;
  align-items: center;
  height: 100%;
  gap: 16px;
}

.stat-icon {
  width: 60px;
  height: 60px;
  border-radius: 12px;
  display: flex;
  align-items: center;
  justify-content: center;
  color: white;
}

.stat-info {
  flex: 1;
}

.stat-number {
  font-size: 32px;
  font-weight: bold;
  color: #303133;
  line-height: 1;
  margin-bottom: 4px;
}

.stat-label {
  font-size: 14px;
  color: #909399;
}

/* 图表部分 */
.charts-section {
  margin-bottom: 24px;
}

.chart-card {
  margin-bottom: 20px;
}

.chart-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
}

.chart-title {
  display: flex;
  align-items: center;
  gap: 8px;
  font-size: 16px;
  font-weight: 600;
  margin: 0;
  color: #303133;
}

.chart-container {
  height: 300px;
  position: relative;
}

.trend-chart,
.score-chart {
  width: 100%;
  height: 100%;
}

.score-legend {
  margin-top: 16px;
  display: flex;
  flex-direction: column;
  gap: 8px;
}

.legend-item {
  display: flex;
  align-items: center;
  gap: 8px;
  font-size: 14px;
}

.legend-color {
  width: 12px;
  height: 12px;
  border-radius: 2px;
}

.legend-text {
  color: #606266;
}

/* 分类统计 */
.category-stats {
  display: flex;
  flex-direction: column;
  gap: 20px;
  padding: 8px 0;
}

.category-item {
  padding: 16px;
  background: #fafafa;
  border-radius: 8px;
  transition: all 0.2s ease;
}

.category-item:hover {
  background: #f0f2f5;
}

.category-info {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 8px;
}

.category-name {
  font-size: 14px;
  font-weight: 500;
  color: #303133;
}

.category-score {
  font-size: 16px;
  font-weight: bold;
  color: #409eff;
}

.category-details {
  display: flex;
  justify-content: space-between;
  margin-top: 8px;
  font-size: 12px;
  color: #909399;
}

/* 难度分析 */
.difficulty-stats {
  display: flex;
  flex-direction: column;
  gap: 24px;
  padding: 8px 0;
}

.difficulty-item {
  padding: 20px;
  border: 1px solid #e4e7ed;
  border-radius: 8px;
  transition: all 0.2s ease;
}

.difficulty-item:hover {
  border-color: #c0c4cc;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.05);
}

.difficulty-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 16px;
}

.difficulty-info {
  display: flex;
  align-items: center;
  gap: 12px;
}

.difficulty-score {
  font-size: 14px;
  font-weight: 500;
  color: #606266;
}

.difficulty-details {
  display: flex;
  flex-direction: column;
  gap: 8px;
}

.detail-row {
  display: flex;
  justify-content: space-between;
  font-size: 13px;
  color: #606266;
}

.detail-row span:last-child {
  font-weight: 500;
  color: #303133;
}

/* 记录部分 */
.records-card {
  margin-bottom: 20px;
}

.records-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
}

.records-title {
  display: flex;
  align-items: center;
  gap: 8px;
  font-size: 16px;
  font-weight: 600;
  margin: 0;
  color: #303133;
}

.records-controls {
  display: flex;
  align-items: center;
  gap: 8px;
}

.records-table {
  margin-bottom: 20px;
}

.question-info {
  padding: 4px 0;
}

.question-title {
  font-size: 14px;
  font-weight: 500;
  color: #303133;
  margin-bottom: 4px;
}

.question-meta {
  display: flex;
  align-items: center;
  gap: 8px;
}

.question-category {
  font-size: 12px;
  color: #909399;
}

.score-badge {
  padding: 4px 8px;
  border-radius: 4px;
  font-size: 12px;
  font-weight: 500;
  color: white;
}

.score-1 { background: #f56c6c; }
.score-2 { background: #e6a23c; }
.score-3 { background: #409eff; }
.score-4, .score-5 { background: #67c23a; }

.duration-text,
.time-text {
  font-size: 12px;
  color: #909399;
}

.pagination-wrapper {
  display: flex;
  justify-content: center;
  padding-top: 16px;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .chart-header {
    flex-direction: column;
    align-items: flex-start;
    gap: 12px;
  }

  .chart-controls {
    width: 100%;
  }

  .chart-container {
    height: 250px;
  }

  .records-header {
    flex-direction: column;
    align-items: flex-start;
    gap: 12px;
  }

  .records-controls {
    width: 100%;
    justify-content: flex-start;
  }

  .records-table {
    font-size: 12px;
  }

  .stat-number {
    font-size: 24px;
  }

  .stat-icon {
    width: 48px;
    height: 48px;
  }
}

@media (max-width: 480px) {
  .difficulty-header {
    flex-direction: column;
    align-items: flex-start;
    gap: 12px;
  }

  .difficulty-progress {
    align-self: center;
  }

  .category-details {
    flex-direction: column;
    gap: 4px;
  }
}
</style>