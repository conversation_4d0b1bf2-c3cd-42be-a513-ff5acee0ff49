<template>
  <div class="review-container">
    <!-- 复习设置卡片 -->
    <el-card v-if="!currentQuestion" class="settings-card" shadow="never">
      <template #header>
        <div class="card-header">
          <h3 class="card-title">
            <el-icon><Setting /></el-icon>
            开始复习
          </h3>
        </div>
      </template>

      <div class="settings-content">
        <el-row :gutter="20">
          <el-col :lg="12" :md="24" :sm="24">
            <div class="setting-section">
              <h4 class="section-title">筛选条件</h4>
              <el-form :model="reviewSettings" label-width="80px">
                <el-form-item label="分类">
                  <el-select
                    v-model="reviewSettings.category"
                    placeholder="全部分类"
                    clearable
                    class="full-width"
                  >
                    <el-option
                      v-for="category in categories"
                      :key="category.value"
                      :label="category.label"
                      :value="category.value"
                    />
                  </el-select>
                </el-form-item>

                <el-form-item label="难度">
                  <el-select
                    v-model="reviewSettings.difficulty"
                    placeholder="全部难度"
                    clearable
                    class="full-width"
                  >
                    <el-option label="简单" value="easy" />
                    <el-option label="中等" value="medium" />
                    <el-option label="困难" value="hard" />
                  </el-select>
                </el-form-item>

                <el-form-item label="复习模式">
                  <el-radio-group v-model="reviewSettings.mode">
                    <el-radio value="random">随机题目</el-radio>
                    <el-radio value="weak">薄弱题目</el-radio>
                  </el-radio-group>
                </el-form-item>
              </el-form>
            </div>
          </el-col>

          <el-col :lg="12" :md="24" :sm="24">
            <div class="setting-section">
              <h4 class="section-title">复习统计</h4>
              <div class="stats-grid">
                <div class="stat-item">
                  <div class="stat-number">{{ availableQuestions }}</div>
                  <div class="stat-label">可复习题目</div>
                </div>
                <div class="stat-item">
                  <div class="stat-number">{{ todayReviewed }}</div>
                  <div class="stat-label">今日已复习</div>
                </div>
                <div class="stat-item">
                  <div class="stat-number">{{ needReviewCount }}</div>
                  <div class="stat-label">需要复习</div>
                </div>
              </div>
            </div>
          </el-col>
        </el-row>

        <div class="start-section">
          <el-button
            type="primary"
            size="large"
            :icon="Reading"
            @click="startReview"
            :loading="loading"
            :disabled="availableQuestions === 0"
          >
            开始复习
          </el-button>
          
          <p class="start-tip">
            {{ availableQuestions === 0 
              ? '暂无符合条件的题目' 
              : `准备好了吗？开始今天的学习之旅！` 
            }}
          </p>
        </div>
      </div>
    </el-card>

    <!-- 复习题目卡片 -->
    <div v-if="currentQuestion" class="review-section">
      <!-- 复习头部 -->
      <el-card class="review-header-card" shadow="never">
        <div class="review-header">
          <div class="progress-info">
            <span class="progress-text">
              进度: {{ reviewProgress.current }} / {{ reviewProgress.total }}
            </span>
            <el-progress 
              :percentage="reviewProgress.percentage" 
              :stroke-width="6"
              :color="progressColor"
              class="progress-bar"
            />
          </div>
          
          <div class="header-actions">
            <el-button
              type="info"
              :icon="Refresh"
              @click="getNextQuestion"
              :loading="loading"
            >
              换一题
            </el-button>
            <el-button
              type="danger"
              :icon="CloseBold"
              @click="exitReview"
            >
              退出复习
            </el-button>
          </div>
        </div>
      </el-card>

      <!-- 题目内容 -->
      <el-card class="question-card" shadow="never">
        <div class="question-header">
          <div class="question-meta">
            <el-tag :type="getDifficultyType(currentQuestion.difficulty)" size="large">
              {{ getDifficultyText(currentQuestion.difficulty) }}
            </el-tag>
            <el-tag type="info" size="large">
              {{ currentQuestion.category }}
            </el-tag>
          </div>
          
          <div class="question-actions">
            <el-button
              type="text"
              :icon="showAnswer ? EyeClose : View"
              @click="toggleAnswer"
            >
              {{ showAnswer ? '隐藏答案' : '查看答案' }}
            </el-button>
          </div>
        </div>

        <div class="question-content">
          <h2 class="question-title">{{ currentQuestion.title }}</h2>
          <div class="question-body">
            <p class="question-text">{{ currentQuestion.content }}</p>
          </div>

          <!-- 答案区域 -->
          <div v-if="showAnswer && currentQuestion.answer" class="answer-section">
            <div class="answer-header">
              <h3 class="answer-title">
                <el-icon><Document /></el-icon>
                参考答案
              </h3>
            </div>
            <div class="answer-content">
              {{ currentQuestion.answer }}
            </div>
          </div>
        </div>
      </el-card>

      <!-- 评分系统 -->
      <el-card class="scoring-card" shadow="never">
        <div class="scoring-content">
          <h3 class="scoring-title">
            <el-icon><Star /></el-icon>
            给这道题打分
          </h3>
          <p class="scoring-subtitle">根据你对这道题的掌握程度进行评分</p>

          <div class="score-options">
            <div
              v-for="score in scoreOptions"
              :key="score.value"
              class="score-option"
              :class="{ 
                'selected': selectedScore === score.value,
                [`score-${score.value}`]: true 
              }"
              @click="selectScore(score.value)"
            >
              <div class="score-number">{{ score.value }}</div>
              <div class="score-text">
                <div class="score-label">{{ score.label }}</div>
                <div class="score-description">{{ score.description }}</div>
              </div>
            </div>
          </div>

          <div class="scoring-actions">
            <el-button
              type="primary"
              size="large"
              :disabled="selectedScore === null"
              :loading="submitting"
              @click="submitScore"
            >
              提交评分并继续
            </el-button>
          </div>
        </div>
      </el-card>
    </div>

    <!-- 复习完成对话框 -->
    <el-dialog
      v-model="completionDialog.visible"
      title="复习完成"
      width="600px"
      :close-on-click-modal="false"
      class="completion-dialog"
    >
      <div class="completion-content">
        <div class="completion-header">
          <el-icon :size="48" color="#67c23a">
            <CircleCheck />
          </el-icon>
          <h3 class="completion-title">恭喜完成复习！</h3>
        </div>

        <div class="completion-stats">
          <div class="stat-row">
            <span class="stat-label">复习题目：</span>
            <span class="stat-value">{{ completionDialog.stats.total }} 道</span>
          </div>
          <div class="stat-row">
            <span class="stat-label">平均得分：</span>
            <span class="stat-value score-highlight">
              {{ completionDialog.stats.avgScore.toFixed(1) }} 分
            </span>
          </div>
          <div class="stat-row">
            <span class="stat-label">用时：</span>
            <span class="stat-value">{{ completionDialog.stats.duration }}</span>
          </div>
        </div>

        <div class="completion-suggestions">
          <h4>学习建议：</h4>
          <ul class="suggestion-list">
            <li v-for="suggestion in completionDialog.suggestions" :key="suggestion">
              {{ suggestion }}
            </li>
          </ul>
        </div>
      </div>

      <template #footer>
        <div class="dialog-footer">
          <el-button @click="completionDialog.visible = false">
            返回设置
          </el-button>
          <el-button type="primary" @click="startNewReview">
            再来一轮
          </el-button>
          <el-button type="success" @click="goToStats">
            查看统计
          </el-button>
        </div>
      </template>
    </el-dialog>
  </div>
</template>

<script setup>
import { ref, reactive, computed, onMounted } from 'vue'
import { useRouter, useRoute } from 'vue-router'
import { useQuestionStore } from '@/stores/question'
import { useReviewStore } from '@/stores/review'
import { ElMessage, ElMessageBox } from 'element-plus'
import {
  Setting, Reading, Refresh, CloseBold, View, EyeClose,
  Document, Star, CircleCheck
} from '@element-plus/icons-vue'

// Store 和 Router
const router = useRouter()
const route = useRoute()
const questionStore = useQuestionStore()
const reviewStore = useReviewStore()

// 响应式数据
const loading = ref(false)
const submitting = ref(false)
const showAnswer = ref(false)
const selectedScore = ref(null)
const currentQuestion = ref(null)
const reviewStartTime = ref(null)
const reviewedQuestions = ref([])

const reviewSettings = reactive({
  category: '',
  difficulty: '',
  mode: 'random' // random 或 weak
})

const completionDialog = reactive({
  visible: false,
  stats: {
    total: 0,
    avgScore: 0,
    duration: ''
  },
  suggestions: []
})

// 计算属性
const categories = computed(() => [
  { label: 'JavaScript', value: 'JavaScript' },
  { label: 'Vue.js', value: 'Vue.js' },
  { label: 'React', value: 'React' },
  { label: 'Node.js', value: 'Node.js' },
  { label: 'CSS', value: 'CSS' },
  { label: '算法', value: '算法' },
  { label: '网络协议', value: '网络协议' },
  { label: '数据库', value: '数据库' }
])

const scoreOptions = computed(() => [
  {
    value: 1,
    label: '完全不会',
    description: '完全不了解，需要重点学习'
  },
  {
    value: 2,
    label: '有印象',
    description: '有基本概念，但不够深入'
  },
  {
    value: 3,
    label: '基本掌握',
    description: '理解基本要点，可以应用'
  },
  {
    value: 4,
    label: '熟练掌握',
    description: '熟悉细节，能够举一反三'
  },
  {
    value: 5,
    label: '非常熟练',
    description: '完全掌握，可以指导他人'
  }
])

const availableQuestions = computed(() => {
  // 模拟可用题目数量
  return 15
})

const todayReviewed = computed(() => {
  return reviewStore.todayStats.count
})

const needReviewCount = computed(() => {
  return reviewStore.needReviewCount
})

const reviewProgress = computed(() => {
  const current = reviewedQuestions.value.length + 1
  const total = Math.max(current, 10) // 假设总共复习10道题
  return {
    current,
    total,
    percentage: Math.min((current / total) * 100, 100)
  }
})

const progressColor = computed(() => {
  const percentage = reviewProgress.value.percentage
  if (percentage >= 80) return '#67c23a'
  if (percentage >= 60) return '#e6a23c'
  return '#409eff'
})

// 方法
const startReview = async () => {
  loading.value = true
  try {
    // 获取第一道题目
    await getNextQuestion()
    reviewStartTime.value = Date.now()
  } finally {
    loading.value = false
  }
}

const getNextQuestion = async () => {
  loading.value = true
  try {
    // 模拟获取题目
    const mockQuestions = [
      {
        id: 1,
        title: 'JavaScript闭包的原理和应用',
        content: '请详细解释JavaScript中闭包的概念，包括其工作原理、常见应用场景，并举例说明闭包在实际开发中的作用。',
        category: 'JavaScript',
        difficulty: 'medium',
        answer: '闭包是指有权访问另一个函数作用域中变量的函数。闭包创建的常见方式是在一个函数内部创建另一个函数...'
      },
      {
        id: 2,
        title: 'Vue3 Composition API的优势',
        content: '对比Options API，Composition API有哪些优势？请结合具体实例说明Composition API如何提高代码的可维护性和复用性。',
        category: 'Vue.js',
        difficulty: 'hard',
        answer: 'Composition API提供了更好的逻辑复用性、类型推断支持、更灵活的组件组织方式...'
      },
      {
        id: 3,
        title: 'HTTP状态码详解',
        content: '请详细说明常见HTTP状态码的含义，包括2xx、3xx、4xx、5xx系列，并举例说明它们在实际应用中的使用场景。',
        category: '网络协议',
        difficulty: 'easy',
        answer: 'HTTP状态码用于表示HTTP请求的处理结果。2xx表示成功，3xx表示重定向，4xx表示客户端错误，5xx表示服务器错误...'
      }
    ]

    // 随机选择一个题目
    const randomIndex = Math.floor(Math.random() * mockQuestions.length)
    currentQuestion.value = mockQuestions[randomIndex]
    
    // 重置状态
    showAnswer.value = false
    selectedScore.value = null

  } finally {
    loading.value = false
  }
}

const selectScore = (score) => {
  selectedScore.value = score
}

const submitScore = async () => {
  if (selectedScore.value === null) {
    ElMessage.warning('请先选择评分')
    return
  }

  submitting.value = true
  try {
    // 提交复习记录
    const reviewData = {
      questionId: currentQuestion.value.id,
      score: selectedScore.value,
      duration: Date.now() - reviewStartTime.value
    }

    const result = await reviewStore.submitReview(reviewData)
    if (result.success) {
      // 记录已复习题目
      reviewedQuestions.value.push({
        ...currentQuestion.value,
        score: selectedScore.value
      })

      // 检查是否完成复习
      if (reviewedQuestions.value.length >= 10) {
        showCompletionDialog()
      } else {
        // 继续下一题
        await getNextQuestion()
      }
    }
  } finally {
    submitting.value = false
  }
}

const toggleAnswer = () => {
  showAnswer.value = !showAnswer.value
}

const exitReview = async () => {
  try {
    await ElMessageBox.confirm(
      '确定要退出当前复习吗？进度将不会保存。',
      '确认退出',
      {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }
    )
    
    resetReview()
  } catch (error) {
    // 用户取消
  }
}

const showCompletionDialog = () => {
  const totalScore = reviewedQuestions.value.reduce((sum, q) => sum + q.score, 0)
  const avgScore = totalScore / reviewedQuestions.value.length
  const duration = Math.round((Date.now() - reviewStartTime.value) / 1000 / 60)

  completionDialog.stats = {
    total: reviewedQuestions.value.length,
    avgScore,
    duration: `${duration} 分钟`
  }

  // 生成学习建议
  const suggestions = []
  if (avgScore < 3) {
    suggestions.push('建议加强基础知识学习，多做练习')
    suggestions.push('可以先从简单题目开始，逐步提升难度')
  } else if (avgScore < 4) {
    suggestions.push('基础不错，可以尝试更有挑战性的题目')
    suggestions.push('建议深入学习相关知识点的细节')
  } else {
    suggestions.push('掌握程度很好，继续保持学习节奏')
    suggestions.push('可以尝试教授他人或写技术博客来巩固知识')
  }

  completionDialog.suggestions = suggestions
  completionDialog.visible = true
}

const startNewReview = () => {
  completionDialog.visible = false
  resetReview()
  startReview()
}

const goToStats = () => {
  completionDialog.visible = false
  router.push('/stats')
}

const resetReview = () => {
  currentQuestion.value = null
  reviewedQuestions.value = []
  showAnswer.value = false
  selectedScore.value = null
  reviewStartTime.value = null
}

const getDifficultyType = (difficulty) => {
  const types = {
    easy: 'success',
    medium: 'warning',
    hard: 'danger'
  }
  return types[difficulty] || 'info'
}

const getDifficultyText = (difficulty) => {
  const texts = {
    easy: '简单',
    medium: '中等',
    hard: '困难'
  }
  return texts[difficulty] || '未知'
}

// 生命周期
onMounted(() => {
  // 如果URL中有题目ID，直接开始复习
  const questionId = route.query.id
  if (questionId) {
    startReview()
  }
})
</script>

<style scoped>
.review-container {
  padding: 0;
}

/* 设置卡片 */
.settings-card {
  max-width: 1000px;
  margin: 0 auto;
}

.settings-content {
  padding: 20px 0;
}

.setting-section {
  margin-bottom: 32px;
}

.section-title {
  font-size: 16px;
  font-weight: 600;
  color: #303133;
  margin: 0 0 16px 0;
  padding-bottom: 8px;
  border-bottom: 2px solid #e4e7ed;
}

.full-width {
  width: 100%;
}

.stats-grid {
  display: grid;
  grid-template-columns: repeat(3, 1fr);
  gap: 16px;
}

.stat-item {
  text-align: center;
  padding: 16px;
  background: #f8f9fa;
  border-radius: 8px;
}

.stat-number {
  font-size: 24px;
  font-weight: bold;
  color: #409eff;
  margin-bottom: 4px;
}

.stat-label {
  font-size: 12px;
  color: #909399;
}

.start-section {
  text-align: center;
  margin-top: 40px;
}

.start-tip {
  font-size: 14px;
  color: #909399;
  margin-top: 12px;
}

/* 复习部分 */
.review-section {
  max-width: 900px;
  margin: 0 auto;
}

.review-header-card {
  margin-bottom: 20px;
}

.review-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
}

.progress-info {
  flex: 1;
  margin-right: 20px;
}

.progress-text {
  display: block;
  font-size: 14px;
  color: #606266;
  margin-bottom: 8px;
}

.progress-bar {
  max-width: 300px;
}

.header-actions {
  display: flex;
  gap: 8px;
}

/* 题目卡片 */
.question-card {
  margin-bottom: 20px;
}

.question-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  margin-bottom: 24px;
  padding-bottom: 16px;
  border-bottom: 1px solid #e4e7ed;
}

.question-meta {
  display: flex;
  gap: 8px;
}

.question-content {
  padding: 0;
}

.question-title {
  font-size: 22px;
  font-weight: 600;
  color: #303133;
  margin: 0 0 20px 0;
  line-height: 1.4;
}

.question-body {
  margin-bottom: 24px;
}

.question-text {
  font-size: 16px;
  line-height: 1.8;
  color: #606266;
  margin: 0;
  white-space: pre-wrap;
}

/* 答案区域 */
.answer-section {
  margin-top: 32px;
  padding: 20px;
  background: #f8f9fa;
  border-radius: 8px;
  border-left: 4px solid #409eff;
}

.answer-header {
  margin-bottom: 16px;
}

.answer-title {
  display: flex;
  align-items: center;
  gap: 8px;
  font-size: 16px;
  font-weight: 600;
  color: #303133;
  margin: 0;
}

.answer-content {
  font-size: 15px;
  line-height: 1.8;
  color: #606266;
  white-space: pre-wrap;
}

/* 评分系统 */
.scoring-card {
  margin-bottom: 20px;
}

.scoring-content {
  text-align: center;
  padding: 20px 0;
}

.scoring-title {
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 8px;
  font-size: 18px;
  font-weight: 600;
  color: #303133;
  margin: 0 0 8px 0;
}

.scoring-subtitle {
  font-size: 14px;
  color: #909399;
  margin: 0 0 32px 0;
}

.score-options {
  display: flex;
  flex-direction: column;
  gap: 12px;
  max-width: 600px;
  margin: 0 auto 32px;
}

.score-option {
  display: flex;
  align-items: center;
  padding: 16px 20px;
  background: #fafafa;
  border: 2px solid #e4e7ed;
  border-radius: 12px;
  cursor: pointer;
  transition: all 0.3s ease;
}

.score-option:hover {
  border-color: #c0c4cc;
  transform: translateY(-1px);
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
}

.score-option.selected {
  background: white;
  border-color: #409eff;
  box-shadow: 0 4px 20px rgba(64, 158, 255, 0.2);
}

.score-number {
  width: 48px;
  height: 48px;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 20px;
  font-weight: bold;
  border-radius: 50%;
  margin-right: 16px;
  color: white;
}

.score-1 .score-number { background: #f56c6c; }
.score-2 .score-number { background: #e6a23c; }
.score-3 .score-number { background: #409eff; }
.score-4 .score-number { background: #67c23a; }
.score-5 .score-number { background: #67c23a; }

.score-text {
  flex: 1;
  text-align: left;
}

.score-label {
  font-size: 16px;
  font-weight: 500;
  color: #303133;
  margin-bottom: 4px;
}

.score-description {
  font-size: 13px;
  color: #909399;
  line-height: 1.4;
}

.scoring-actions {
  margin-top: 32px;
}

/* 完成对话框 */
.completion-content {
  text-align: center;
  padding: 20px 0;
}

.completion-header {
  margin-bottom: 24px;
}

.completion-title {
  font-size: 20px;
  font-weight: 600;
  color: #303133;
  margin: 12px 0 0 0;
}

.completion-stats {
  background: #f8f9fa;
  border-radius: 8px;
  padding: 20px;
  margin-bottom: 24px;
}

.stat-row {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 12px;
  font-size: 15px;
}

.stat-row:last-child {
  margin-bottom: 0;
}

.stat-label {
  color: #606266;
}

.stat-value {
  font-weight: 500;
  color: #303133;
}

.score-highlight {
  color: #67c23a;
  font-weight: bold;
  font-size: 16px;
}

.completion-suggestions {
  text-align: left;
}

.completion-suggestions h4 {
  font-size: 16px;
  font-weight: 600;
  color: #303133;
  margin: 0 0 12px 0;
}

.suggestion-list {
  margin: 0;
  padding-left: 20px;
}

.suggestion-list li {
  font-size: 14px;
  color: #606266;
  line-height: 1.6;
  margin-bottom: 8px;
}

.dialog-footer {
  display: flex;
  justify-content: center;
  gap: 12px;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .review-header {
    flex-direction: column;
    align-items: stretch;
    gap: 16px;
  }

  .progress-info {
    margin-right: 0;
  }

  .header-actions {
    justify-content: center;
  }

  .question-header {
    flex-direction: column;
    align-items: flex-start;
    gap: 12px;
  }

  .question-title {
    font-size: 18px;
  }

  .question-text {
    font-size: 15px;
  }

  .stats-grid {
    grid-template-columns: 1fr;
    gap: 12px;
  }

  .score-option {
    padding: 12px 16px;
  }

  .score-number {
    width: 40px;
    height: 40px;
    font-size: 18px;
    margin-right: 12px;
  }

  .score-label {
    font-size: 15px;
  }

  .score-description {
    font-size: 12px;
  }
}

@media (max-width: 480px) {
  .question-title {
    font-size: 16px;
  }

  .question-text {
    font-size: 14px;
  }

  .scoring-title {
    font-size: 16px;
  }
}
</style>