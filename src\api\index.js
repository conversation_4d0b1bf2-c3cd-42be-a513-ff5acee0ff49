import request from '@/utils/request'

// 认证相关 API
export const authApi = {
  // 登录
  login(data) {
    return request({
      url: '/auth/login',
      method: 'post',
      data
    })
  },

  // 刷新Token
  refreshToken() {
    return request({
      url: '/auth/refresh',
      method: 'post'
    })
  },

  // 获取用户信息
  getUserInfo() {
    return request({
      url: '/auth/user',
      method: 'get'
    })
  },

  // 修改密码
  changePassword(data) {
    return request({
      url: '/auth/change-password',
      method: 'post',
      data
    })
  },

  // 登出
  logout() {
    return request({
      url: '/auth/logout',
      method: 'post'
    })
  }
}

// 题目相关 API
export const questionApi = {
  // 获取题目列表
  getQuestions(params = {}) {
    return request({
      url: '/questions',
      method: 'get',
      params
    })
  },

  // 获取题目详情
  getQuestion(id) {
    return request({
      url: `/questions/${id}`,
      method: 'get'
    })
  },

  // 创建题目
  createQuestion(data) {
    return request({
      url: '/questions',
      method: 'post',
      data
    })
  },

  // 更新题目
  updateQuestion(id, data) {
    return request({
      url: `/questions/${id}`,
      method: 'put',
      data
    })
  },

  // 删除题目
  deleteQuestion(id) {
    return request({
      url: `/questions/${id}`,
      method: 'delete'
    })
  },

  // 批量删除题目
  batchDeleteQuestions(ids) {
    return request({
      url: '/questions',
      method: 'delete',
      data: ids
    })
  },

  // 获取随机题目（用于复习）
  getRandomQuestion(params = {}) {
    return request({
      url: '/questions/random',
      method: 'get',
      params
    })
  },

  // 获取待复习题目
  getReviewQuestions(params = {}) {
    return request({
      url: '/questions/review',
      method: 'get',
      params
    })
  },

  // 根据分类获取题目
  getQuestionsByCategory(categoryId, params = {}) {
    return request({
      url: `/questions/category/${categoryId}`,
      method: 'get',
      params
    })
  },

  // 根据难度获取题目
  getQuestionsByDifficulty(difficulty, params = {}) {
    return request({
      url: `/questions/difficulty/${difficulty}`,
      method: 'get',
      params
    })
  },

  // 搜索题目
  searchQuestions(params = {}) {
    return request({
      url: '/questions/search',
      method: 'get',
      params
    })
  }
}

// 分类管理相关 API
export const categoryApi = {
  // 获取所有分类
  getCategories() {
    return request({
      url: '/categories',
      method: 'get'
    })
  },

  // 获取顶级分类
  getTopCategories() {
    return request({
      url: '/categories/top',
      method: 'get'
    })
  },

  // 获取子分类
  getChildCategories(parentId) {
    return request({
      url: `/categories/${parentId}/children`,
      method: 'get'
    })
  },

  // 获取分类详情
  getCategory(id) {
    return request({
      url: `/categories/${id}`,
      method: 'get'
    })
  },

  // 创建分类
  createCategory(data) {
    return request({
      url: '/categories',
      method: 'post',
      data
    })
  },

  // 更新分类
  updateCategory(id, data) {
    return request({
      url: `/categories/${id}`,
      method: 'put',
      data
    })
  },

  // 删除分类
  deleteCategory(id) {
    return request({
      url: `/categories/${id}`,
      method: 'delete'
    })
  },

  // 获取分类树
  getCategoryTree() {
    return request({
      url: '/categories/tree',
      method: 'get'
    })
  }
}

// 复习记录相关 API
export const reviewApi = {
  // 提交复习记录
  submitReview(data) {
    return request({
      url: '/reviews',
      method: 'post',
      data
    })
  },

  // 获取最近复习记录
  getRecentReviews(params = {}) {
    return request({
      url: '/reviews/recent',
      method: 'get',
      params
    })
  },

  // 获取题目的复习历史
  getQuestionReviews(questionId) {
    return request({
      url: `/reviews/question/${questionId}`,
      method: 'get'
    })
  },

  // 获取需要复习的记录
  getNeededReviews() {
    return request({
      url: '/reviews/needed',
      method: 'get'
    })
  },

  // 获取推荐复习的题目
  getRecommendedQuestions(params = {}) {
    return request({
      url: '/reviews/recommended',
      method: 'get',
      params
    })
  }
}

// 统计相关 API
export const statsApi = {
  // 获取总体统计数据
  getOverallStats() {
    return request({
      url: '/stats',
      method: 'get'
    })
  },

  // 获取复习统计
  getReviewStats(params = {}) {
    return request({
      url: '/stats/reviews',
      method: 'get',
      params
    })
  },

  // 获取分类统计数据
  getCategoryStats() {
    return request({
      url: '/stats/categories',
      method: 'get'
    })
  },

  // 获取难度统计数据
  getDifficultyStats() {
    return request({
      url: '/stats/difficulty',
      method: 'get'
    })
  },

  // 获取复习趋势数据
  getReviewTrend(params = {}) {
    return request({
      url: '/stats/trend',
      method: 'get',
      params
    })
  }
}