<template>
  <div id="app">
    <router-view />
  </div>
</template>

<script setup>
import { onMounted } from 'vue'
import { useAuthStore } from '@/stores/auth'

const authStore = useAuthStore()

onMounted(() => {
  // 初始化认证状态
  authStore.initAuth()
})
</script>

<style>
#app {
  height: 100vh;
  overflow: hidden;
}

/* 全局滚动条样式 */
::-webkit-scrollbar {
  width: 6px;
  height: 6px;
}

::-webkit-scrollbar-track {
  background: #f1f1f1;
  border-radius: 3px;
}

::-webkit-scrollbar-thumb {
  background: #c1c1c1;
  border-radius: 3px;
}

::-webkit-scrollbar-thumb:hover {
  background: #a8a8a8;
}

/* Element Plus 样式优化 */
.el-loading-mask {
  background-color: rgba(255, 255, 255, 0.8);
}

.el-message {
  min-width: 300px;
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.15);
}

.el-drawer__header {
  margin-bottom: 0;
  padding: 20px;
  border-bottom: 1px solid #e4e7ed;
}

.el-card {
  transition: transform 0.2s ease, box-shadow 0.2s ease;
}

.el-card:hover {
  transform: translateY(-2px);
  box-shadow: 0 8px 30px rgba(0, 0, 0, 0.12);
}

/* 表格优化 */
.el-table {
  border-radius: 8px;
  overflow: hidden;
}

.el-table__header th {
  background-color: #fafafa;
  font-weight: 600;
}

/* 按钮组优化 */
.el-button-group .el-button {
  margin: 0;
}

/* 表单优化 */
.el-form-item__label {
  font-weight: 500;
}

.el-input__wrapper,
.el-textarea__inner,
.el-select .el-input__wrapper {
  transition: all 0.2s ease;
}

.el-input__wrapper:hover,
.el-textarea__inner:hover,
.el-select:hover .el-input__wrapper {
  box-shadow: 0 4px 12px rgba(64, 158, 255, 0.15);
}
</style>
