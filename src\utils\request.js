import axios from 'axios'
import { ElMessage, ElLoading } from 'element-plus'
import router from '@/router'
import { useAuthStore } from '@/stores/auth'

// 创建 axios 实例
const request = axios.create({
  baseURL: import.meta.env.VITE_API_BASE_URL || 'http://localhost:8080/api',
  timeout: 10000,
  headers: {
    'Content-Type': 'application/json'
  }
})

let loadingInstance = null
let isRefreshing = false
let failedQueue = []

// 处理失败的请求队列
const processQueue = (error, token = null) => {
  failedQueue.forEach(({ resolve, reject }) => {
    if (error) {
      reject(error)
    } else {
      resolve(token)
    }
  })
  
  failedQueue = []
}

// 请求拦截器
request.interceptors.request.use(
  (config) => {
    // 显示加载动画
    if (config.showLoading !== false) {
      loadingInstance = ElLoading.service({
        lock: true,
        text: '加载中...',
        background: 'rgba(0, 0, 0, 0.7)'
      })
    }

    // 添加 token
    const token = localStorage.getItem('token')
    if (token) {
      config.headers.Authorization = `Bearer ${token}`
    }
    return config
  },
  (error) => {
    if (loadingInstance) {
      loadingInstance.close()
    }
    return Promise.reject(error)
  }
)

// 响应拦截器
request.interceptors.response.use(
  (response) => {
    if (loadingInstance) {
      loadingInstance.close()
    }
    
    const { data } = response
    
    // 统一的响应格式验证
    if (data.success === false || (data.code && data.code !== 200)) {
      const errorMsg = data.message || '请求失败'
      ElMessage.error(errorMsg)
      return Promise.reject(new Error(errorMsg))
    }
    
    return data
  },
  (error) => {
    if (loadingInstance) {
      loadingInstance.close()
    }

    let message = '请求失败'
    
    if (error.response) {
      const { status, data } = error.response
      
      switch (status) {
      case 401: {
        const originalRequest = error.config
        
        // 如果是刷新token的请求失败，直接跳转登录页
        if (originalRequest.url?.includes('/auth/refresh')) {
          message = '登录已过期，请重新登录'
          const authStore = useAuthStore()
          authStore.clearAuth()
          router.push('/login')
          break
        }
        
        // 如果正在刷新token，将请求加入队列
        if (isRefreshing) {
          return new Promise((resolve, reject) => {
            failedQueue.push({ resolve, reject })
          }).then(token => {
            originalRequest.headers.Authorization = `Bearer ${token}`
            return request(originalRequest)
          }).catch(err => {
            return Promise.reject(err)
          })
        }
        
        originalRequest._retry = true
        isRefreshing = true
        
        // 尝试刷新token
        const authStore = useAuthStore()
        return authStore.refreshToken()
          .then((newToken) => {
            if (newToken) {
              processQueue(null, newToken)
              originalRequest.headers.Authorization = `Bearer ${newToken}`
              return request(originalRequest)
            } else {
              throw new Error('Token refresh failed')
            }
          })
          .catch((refreshError) => {
            processQueue(refreshError, null)
            message = '登录已过期，请重新登录'
            authStore.clearAuth()
            router.push('/login')
            return Promise.reject(refreshError)
          })
          .finally(() => {
            isRefreshing = false
          })
      }
        case 403:
          message = '拒绝访问'
          break
        case 404:
          message = '请求地址出错'
          break
        case 500:
          message = '服务器内部错误'
          break
        default:
          message = data?.message || `连接错误${status}`
      }
    } else if (error.code === 'ECONNABORTED') {
      message = '请求超时'
    } else if (error.message === 'Network Error') {
      message = '网络连接异常'
    }

    ElMessage.error(message)
    return Promise.reject(error)
  }
)

export default request