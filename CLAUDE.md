# CLAUDE.md

This file provides guidance to <PERSON> (claude.ai/code) when working with code in this repository.

## Project Overview

This is a Vue 3 frontend application for an interview question management system. It allows users to manage question banks, conduct review sessions, and track learning statistics.

## Core Architecture

- **Framework**: Vue 3 with Composition API
- **UI Library**: Element Plus with custom styling and global scrollbar customization
- **State Management**: Pinia stores (auth, questions, reviews)  
- **Routing**: Vue Router with authentication guards
- **HTTP Client**: Axios with interceptors for loading states and auth tokens
- **Build Tool**: Vite with Vue DevTools integration

## Development Commands

```bash
# Install dependencies
npm install

# Start development server
npm run dev

# Build for production  
npm run build

# Preview production build
npm run preview

# Lint and fix code
npm run lint

# Format code with Prettier
npm run format
```

## Key Components & Structure

### State Management (Pinia Stores)
- **stores/auth.js**: Authentication store (previously counter.js - naming fixed)
- **stores/question.js**: Question management with filtering, pagination, and CRUD operations
- **stores/review.js**: Review session and statistics management  
- **stores/category.js**: **NEW** - Category management with tree structure support

### API Layer
- **api/index.js**: Centralized API definitions for auth, questions, reviews, and stats
- **utils/request.js**: Axios instance with request/response interceptors, loading states, and auth token handling

### Routing & Authentication
- Protected routes using `meta.requiresAuth`
- Authentication guard redirects unauthenticated users to `/login`
- Automatic token-based session management with localStorage persistence

### Views Structure
- **LoginView**: Authentication interface
- **LayoutView**: Main app shell with navigation
- **DashboardView**: Overview and statistics
- **QuestionBankView**: Question management interface  
- **ReviewView**: Interactive review sessions
- **StatsView**: Learning analytics and progress tracking

## Important Implementation Notes

### Authentication Flow
- Tokens stored in localStorage with user info
- Request interceptor automatically adds Bearer tokens
- 401 responses trigger automatic logout and redirect
- Auth state initialized on app mount and route changes

### Question Management  
- Local filtering by category, difficulty, and keywords
- Real-time statistics calculation (difficulty/category distribution)
- CRUD operations with optimistic UI updates

### API Integration
- Base URL configured via `VITE_API_BASE_URL` environment variable
- Default backend URL: `http://localhost:8080/api`
- **Updated**: Pagination starts from 0 (not 1)
- **Updated**: Question fields changed: `category` → `categoryId`, difficulty is now numeric (1-3)
- **Updated**: Review scoring field: `mastery` → `masteryScore`
- Token auto-refresh mechanism implemented
- Global loading states managed through Element Plus loading service
- Standardized error handling with user-friendly messages

### Styling Approach
- Global Element Plus component customization in App.vue
- Custom scrollbar styling for consistent UI
- Hover effects and transitions for enhanced UX
- Responsive design considerations

## Known Architecture Issues

1. **Misleading Store Names**: `stores/counter.js` contains authentication logic, not counter functionality
2. **Token Security**: No token expiration validation or refresh mechanism implemented
3. **Error Handling**: Inconsistent logging patterns (mix of console.log and ElMessage)