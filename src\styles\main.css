/* 全局样式 */
* {
  box-sizing: border-box;
}

body {
  margin: 0;
  padding: 0;
  font-family: 'Helvetica Neue', Helvetica, 'PingFang SC', 'Hiragino Sans GB', 'Microsoft YaHei', '微软雅黑', <PERSON><PERSON>, sans-serif;
  background: #f5f7fa;
  color: #303133;
}

/* 页面容器 */
.page-container {
  min-height: 100vh;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
}

.content-container {
  max-width: 1200px;
  margin: 0 auto;
  padding: 20px;
}

/* 卡片样式 */
.app-card {
  background: white;
  border-radius: 12px;
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.08);
  padding: 24px;
  margin-bottom: 20px;
  border: 1px solid #e4e7ed;
}

/* 标题样式 */
.page-title {
  font-size: 28px;
  font-weight: 600;
  color: #303133;
  margin-bottom: 24px;
  text-align: center;
}

.section-title {
  font-size: 20px;
  font-weight: 600;
  color: #409eff;
  margin-bottom: 16px;
  border-bottom: 2px solid #409eff;
  padding-bottom: 8px;
}

/* 按钮样式增强 */
.el-button {
  border-radius: 8px;
  font-weight: 500;
}

.el-button--primary {
  background: linear-gradient(45deg, #409eff, #66b1ff);
  border: none;
}

.el-button--primary:hover {
  background: linear-gradient(45deg, #66b1ff, #409eff);
  transform: translateY(-1px);
  box-shadow: 0 4px 12px rgba(64, 158, 255, 0.4);
}

/* 表单样式 */
.el-form {
  max-width: 100%;
}

.el-input__wrapper {
  border-radius: 8px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.06);
}

.el-card {
  border-radius: 12px;
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.08);
}

/* 评分样式 */
.score-rating {
  display: flex;
  align-items: center;
  gap: 8px;
  margin: 16px 0;
}

.score-label {
  font-weight: 500;
  color: #606266;
  margin-right: 12px;
}

/* 统计卡片 */
.stat-card {
  text-align: center;
  padding: 20px;
  background: white;
  border-radius: 12px;
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.08);
  transition: transform 0.2s ease;
}

.stat-card:hover {
  transform: translateY(-2px);
  box-shadow: 0 8px 30px rgba(0, 0, 0, 0.12);
}

.stat-number {
  font-size: 32px;
  font-weight: bold;
  color: #409eff;
  margin-bottom: 8px;
}

.stat-label {
  font-size: 14px;
  color: #909399;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .content-container {
    padding: 12px;
  }
  
  .app-card {
    padding: 16px;
    margin-bottom: 12px;
  }
  
  .page-title {
    font-size: 24px;
    margin-bottom: 20px;
  }
  
  .section-title {
    font-size: 18px;
  }
  
  .stat-number {
    font-size: 24px;
  }
  
  /* 移动端表格优化 */
  .el-table {
    font-size: 12px;
  }
  
  .el-table .cell {
    padding: 8px 4px;
  }
}

@media (max-width: 480px) {
  .content-container {
    padding: 8px;
  }
  
  .app-card {
    padding: 12px;
  }
  
  .page-title {
    font-size: 20px;
  }
}

/* 加载动画 */
.fade-in {
  animation: fadeIn 0.5s ease-in;
}

@keyframes fadeIn {
  from {
    opacity: 0;
    transform: translateY(20px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

/* 评分系统颜色 */
.score-1 { color: #f56c6c; }
.score-2 { color: #e6a23c; }
.score-3 { color: #409eff; }
.score-4 { color: #67c23a; }
.score-5 { color: #67c23a; }

/* 难度标签 */
.difficulty-easy { 
  background: #f0f9ff; 
  color: #67c23a; 
  border: 1px solid #67c23a;
}

.difficulty-medium { 
  background: #fffbf0; 
  color: #e6a23c; 
  border: 1px solid #e6a23c;
}

.difficulty-hard { 
  background: #fef0f0; 
  color: #f56c6c; 
  border: 1px solid #f56c6c;
}

/* 侧边栏样式 */
.app-sidebar {
  background: white;
  box-shadow: 2px 0 8px rgba(0, 0, 0, 0.1);
}

.el-menu {
  border: none;
}

.el-menu-item {
  border-radius: 8px;
  margin: 4px 8px;
}

.el-menu-item:hover {
  background: #ecf5ff;
  color: #409eff;
}

.el-menu-item.is-active {
  background: linear-gradient(45deg, #409eff, #66b1ff);
  color: white;
}

/* 头部样式 */
.app-header {
  background: white;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  padding: 0 20px;
}