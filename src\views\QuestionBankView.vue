<template>
  <div class="question-bank-container">
    <!-- 操作栏 -->
    <el-card class="operation-card" shadow="never">
      <div class="operation-content">
        <div class="search-filters">
          <el-input
            v-model="filters.keyword"
            placeholder="搜索题目标题或内容..."
            :prefix-icon="Search"
            clearable
            class="search-input"
            @input="handleSearch"
          />
          
          <el-select
            v-model="filters.categoryId"
            placeholder="选择分类"
            clearable
            class="filter-select"
            @change="handleFilter"
          >
            <el-option
              v-for="category in categoryStore.categories"
              :key="category.id"
              :label="category.name"
              :value="category.id"
            />
          </el-select>

          <el-select
            v-model="filters.difficulty"
            placeholder="选择难度"
            clearable
            class="filter-select"
            @change="handleFilter"
          >
            <el-option label="简单" :value="1" />
            <el-option label="中等" :value="2" />
            <el-option label="困难" :value="3" />
          </el-select>

          <el-button
            @click="resetFilters"
            :icon="Refresh"
          >
            重置
          </el-button>
        </div>

        <div class="operation-buttons">
          <el-button
            type="primary"
            :icon="Plus"
            @click="openCreateDialog"
          >
            添加题目
          </el-button>
          
          <el-button
            type="success"
            :icon="Upload"
            @click="openImportDialog"
          >
            批量导入
          </el-button>
        </div>
      </div>
    </el-card>

    <!-- 题目表格 -->
    <el-card class="table-card" shadow="never">
      <el-table
        v-loading="loading"
        :data="filteredQuestions"
        stripe
        class="question-table"
        :default-sort="{ prop: 'updatedAt', order: 'descending' }"
      >
        <el-table-column
          prop="title"
          label="题目标题"
          min-width="200"
          show-overflow-tooltip
        >
          <template #default="scope">
            <div class="question-title-cell">
              <h4 class="question-title">{{ scope.row.title }}</h4>
              <p class="question-preview">{{ getPreview(scope.row.content) }}</p>
            </div>
          </template>
        </el-table-column>

        <el-table-column
          prop="category"
          label="分类"
          width="120"
          align="center"
        >
          <template #default="scope">
            <el-tag type="info" size="small">
              {{ scope.row.category }}
            </el-tag>
          </template>
        </el-table-column>

        <el-table-column
          prop="difficulty"
          label="难度"
          width="100"
          align="center"
        >
          <template #default="scope">
            <el-tag
              :type="getDifficultyType(scope.row.difficulty)"
              size="small"
            >
              {{ getDifficultyText(scope.row.difficulty) }}
            </el-tag>
          </template>
        </el-table-column>

        <el-table-column
          prop="reviewCount"
          label="复习次数"
          width="100"
          align="center"
        >
          <template #default="scope">
            <span class="review-count">{{ scope.row.reviewCount || 0 }}</span>
          </template>
        </el-table-column>

        <el-table-column
          prop="avgScore"
          label="平均得分"
          width="100"
          align="center"
        >
          <template #default="scope">
            <span 
              class="avg-score"
              :class="`score-${Math.floor(scope.row.avgScore || 0)}`"
            >
              {{ (scope.row.avgScore || 0).toFixed(1) }}分
            </span>
          </template>
        </el-table-column>

        <el-table-column
          prop="updatedAt"
          label="更新时间"
          width="160"
          align="center"
          sortable
        >
          <template #default="scope">
            <span class="update-time">
              {{ formatDate(scope.row.updatedAt) }}
            </span>
          </template>
        </el-table-column>

        <el-table-column
          label="操作"
          width="200"
          align="center"
          fixed="right"
        >
          <template #default="scope">
            <div class="action-buttons">
              <el-button
                type="primary"
                size="small"
                :icon="View"
                @click="previewQuestion(scope.row)"
                circle
                title="预览"
              />
              <el-button
                type="success"
                size="small"
                :icon="Edit"
                @click="editQuestion(scope.row)"
                circle
                title="编辑"
              />
              <el-button
                type="info"
                size="small"
                :icon="Reading"
                @click="reviewQuestion(scope.row)"
                circle
                title="复习"
              />
              <el-button
                type="danger"
                size="small"
                :icon="Delete"
                @click="deleteQuestion(scope.row)"
                circle
                title="删除"
              />
            </div>
          </template>
        </el-table-column>
      </el-table>

      <!-- 分页 -->
      <div class="pagination-wrapper">
        <el-pagination
          v-model:current-page="pagination.current"
          v-model:page-size="pagination.size"
          :page-sizes="[10, 20, 50, 100]"
          :small="isMobile"
          :total="pagination.total"
          layout="total, sizes, prev, pager, next, jumper"
          @current-change="handlePageChange"
          @size-change="handleSizeChange"
        />
      </div>
    </el-card>

    <!-- 添加/编辑题目对话框 -->
    <el-dialog
      v-model="questionDialog.visible"
      :title="questionDialog.isEdit ? '编辑题目' : '添加题目'"
      width="800px"
      :close-on-click-modal="false"
      class="question-dialog"
    >
      <el-form
        ref="questionFormRef"
        :model="questionForm"
        :rules="questionRules"
        label-width="100px"
        class="question-form"
      >
        <el-form-item label="题目标题" prop="title">
          <el-input
            v-model="questionForm.title"
            placeholder="请输入题目标题"
            maxlength="200"
            show-word-limit
          />
        </el-form-item>

        <el-form-item label="题目内容" prop="content">
          <el-input
            v-model="questionForm.content"
            type="textarea"
            :rows="8"
            placeholder="请输入题目详细内容..."
            maxlength="2000"
            show-word-limit
          />
        </el-form-item>

        <el-row :gutter="20">
          <el-col :span="12">
            <el-form-item label="分类" prop="category">
              <el-select
                v-model="questionForm.category"
                placeholder="请选择分类"
                filterable
                allow-create
                class="full-width"
              >
                <el-option
                  v-for="category in categories"
                  :key="category.value"
                  :label="category.label"
                  :value="category.value"
                />
              </el-select>
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="难度" prop="difficulty">
              <el-select v-model="questionForm.difficulty" placeholder="请选择难度" class="full-width">
                <el-option label="简单" value="easy" />
                <el-option label="中等" value="medium" />
                <el-option label="困难" value="hard" />
              </el-select>
            </el-form-item>
          </el-col>
        </el-row>

        <el-form-item label="参考答案">
          <el-input
            v-model="questionForm.answer"
            type="textarea"
            :rows="6"
            placeholder="请输入参考答案（可选）..."
            maxlength="2000"
            show-word-limit
          />
        </el-form-item>

        <el-form-item label="标签">
          <el-input
            v-model="questionForm.tags"
            placeholder="请输入标签，用逗号分隔"
          />
        </el-form-item>
      </el-form>

      <template #footer>
        <div class="dialog-footer">
          <el-button @click="questionDialog.visible = false">
            取消
          </el-button>
          <el-button
            type="primary"
            :loading="submitting"
            @click="submitQuestion"
          >
            {{ questionDialog.isEdit ? '更新' : '保存' }}
          </el-button>
        </div>
      </template>
    </el-dialog>

    <!-- 题目预览对话框 -->
    <el-dialog
      v-model="previewDialog.visible"
      title="题目预览"
      width="700px"
      class="preview-dialog"
    >
      <div v-if="previewDialog.question" class="preview-content">
        <div class="preview-header">
          <h3 class="preview-title">{{ previewDialog.question.title }}</h3>
          <div class="preview-meta">
            <el-tag :type="getDifficultyType(previewDialog.question.difficulty)" size="small">
              {{ getDifficultyText(previewDialog.question.difficulty) }}
            </el-tag>
            <el-tag type="info" size="small">
              {{ previewDialog.question.category }}
            </el-tag>
          </div>
        </div>

        <div class="preview-body">
          <h4>题目内容：</h4>
          <div class="content-text">{{ previewDialog.question.content }}</div>
          
          <div v-if="previewDialog.question.answer" class="answer-section">
            <h4>参考答案：</h4>
            <div class="answer-text">{{ previewDialog.question.answer }}</div>
          </div>

          <div v-if="previewDialog.question.tags" class="tags-section">
            <h4>标签：</h4>
            <div class="tags-list">
              <el-tag
                v-for="tag in previewDialog.question.tags.split(',')"
                :key="tag.trim()"
                size="small"
                class="tag-item"
              >
                {{ tag.trim() }}
              </el-tag>
            </div>
          </div>
        </div>
      </div>
    </el-dialog>
  </div>
</template>

<script setup>
import { ref, reactive, computed, onMounted } from 'vue'
import { useRouter } from 'vue-router'
import { useQuestionStore } from '@/stores/question'
import { useCategoryStore } from '@/stores/category'
import { ElMessage, ElMessageBox } from 'element-plus'
import {
  Search, Plus, Upload, Refresh, View, Edit, Reading, Delete
} from '@element-plus/icons-vue'

// Store 和 Router
const router = useRouter()
const questionStore = useQuestionStore()
const categoryStore = useCategoryStore()

// 响应式数据
const loading = ref(false)
const submitting = ref(false)
const isMobile = ref(window.innerWidth < 768)

const questionFormRef = ref()

const filters = reactive({
  keyword: '',
  categoryId: null,
  difficulty: null
})

const pagination = reactive({
  current: 1,
  size: 20,
  total: 0
})

const questionDialog = reactive({
  visible: false,
  isEdit: false
})

const previewDialog = reactive({
  visible: false,
  question: null
})

const questionForm = reactive({
  title: '',
  content: '',
  category: '',
  difficulty: '',
  answer: '',
  tags: ''
})

// 表单验证规则
const questionRules = {
  title: [
    { required: true, message: '请输入题目标题', trigger: 'blur' },
    { min: 5, max: 200, message: '标题长度为 5-200 个字符', trigger: 'blur' }
  ],
  content: [
    { required: true, message: '请输入题目内容', trigger: 'blur' },
    { min: 10, max: 2000, message: '内容长度为 10-2000 个字符', trigger: 'blur' }
  ],
  category: [
    { required: true, message: '请选择分类', trigger: 'change' }
  ],
  difficulty: [
    { required: true, message: '请选择难度', trigger: 'change' }
  ]
}

// 计算属性
const categories = computed(() => {
  const defaultCategories = [
    { label: 'JavaScript', value: 'JavaScript' },
    { label: 'Vue.js', value: 'Vue.js' },
    { label: 'React', value: 'React' },
    { label: 'Node.js', value: 'Node.js' },
    { label: 'CSS', value: 'CSS' },
    { label: '算法', value: '算法' },
    { label: '网络协议', value: '网络协议' },
    { label: '数据库', value: '数据库' }
  ]
  
  // 合并现有分类
  const existingCategories = [...new Set(questionStore.questions.map(q => q.category))]
  existingCategories.forEach(category => {
    if (!defaultCategories.find(c => c.value === category)) {
      defaultCategories.push({ label: category, value: category })
    }
  })
  
  return defaultCategories
})

const filteredQuestions = computed(() => {
  let filtered = [...mockQuestions] // 实际应用中使用 questionStore.questions
  
  if (filters.keyword) {
    const keyword = filters.keyword.toLowerCase()
    filtered = filtered.filter(q => 
      q.title.toLowerCase().includes(keyword) || 
      q.content.toLowerCase().includes(keyword)
    )
  }
  
  if (filters.category) {
    filtered = filtered.filter(q => q.category === filters.category)
  }
  
  if (filters.difficulty) {
    filtered = filtered.filter(q => q.difficulty === filters.difficulty)
  }
  
  pagination.total = filtered.length
  
  const start = (pagination.current - 1) * pagination.size
  const end = start + pagination.size
  
  return filtered.slice(start, end)
})

// 模拟数据
const mockQuestions = ref([
  {
    id: 1,
    title: 'JavaScript闭包的原理和应用场景',
    content: '请详细解释JavaScript中闭包的概念，并举例说明闭包的实际应用场景。',
    category: 'JavaScript',
    difficulty: 'medium',
    answer: '闭包是指有权访问另一个函数作用域中变量的函数...',
    tags: '闭包,作用域,JavaScript基础',
    reviewCount: 5,
    avgScore: 3.8,
    updatedAt: '2024-01-15T10:30:00Z'
  },
  {
    id: 2,
    title: 'Vue3 Composition API的优势',
    content: '对比Options API，Composition API有哪些优势？请结合实例说明。',
    category: 'Vue.js',
    difficulty: 'hard',
    answer: 'Composition API提供了更好的逻辑复用性...',
    tags: 'Vue3,Composition API,响应式',
    reviewCount: 3,
    avgScore: 4.2,
    updatedAt: '2024-01-14T14:20:00Z'
  },
  {
    id: 3,
    title: 'HTTP和HTTPS的区别',
    content: '详细说明HTTP和HTTPS协议的区别，以及HTTPS的工作原理。',
    category: '网络协议',
    difficulty: 'easy',
    answer: 'HTTP是超文本传输协议，HTTPS是HTTP的安全版本...',
    tags: 'HTTP,HTTPS,网络安全',
    reviewCount: 8,
    avgScore: 4.5,
    updatedAt: '2024-01-13T09:15:00Z'
  }
])

// 方法
const handleSearch = () => {
  pagination.current = 1
}

const handleFilter = () => {
  pagination.current = 1
}

const resetFilters = () => {
  filters.keyword = ''
  filters.category = ''
  filters.difficulty = ''
  pagination.current = 1
}

const handlePageChange = (page) => {
  pagination.current = page
}

const handleSizeChange = (size) => {
  pagination.size = size
  pagination.current = 1
}

const openCreateDialog = () => {
  resetQuestionForm()
  questionDialog.isEdit = false
  questionDialog.visible = true
}

const editQuestion = (question) => {
  Object.assign(questionForm, {
    ...question,
    tags: question.tags || ''
  })
  questionDialog.isEdit = true
  questionDialog.visible = true
}

const previewQuestion = (question) => {
  previewDialog.question = question
  previewDialog.visible = true
}

const reviewQuestion = (question) => {
  router.push(`/review?id=${question.id}`)
}

const deleteQuestion = async (question) => {
  try {
    await ElMessageBox.confirm(
      `确定要删除题目"${question.title}"吗？此操作不可恢复。`,
      '确认删除',
      {
        confirmButtonText: '删除',
        cancelButtonText: '取消',
        type: 'warning',
        confirmButtonClass: 'el-button--danger'
      }
    )

    const result = await questionStore.deleteQuestion(question.id)
    if (result.success) {
      ElMessage.success('题目删除成功')
      loadQuestions()
    }
  } catch (error) {
    // 用户取消删除
  }
}

const submitQuestion = async () => {
  if (!questionFormRef.value) return

  try {
    const valid = await questionFormRef.value.validate()
    if (!valid) return

    submitting.value = true

    const questionData = { ...questionForm }
    let result

    if (questionDialog.isEdit) {
      result = await questionStore.updateQuestion(questionForm.id, questionData)
    } else {
      result = await questionStore.createQuestion(questionData)
    }

    if (result.success) {
      ElMessage.success(questionDialog.isEdit ? '题目更新成功' : '题目添加成功')
      questionDialog.visible = false
      loadQuestions()
    }
  } catch (error) {
    ElMessage.error('操作失败，请重试')
  } finally {
    submitting.value = false
  }
}

const resetQuestionForm = () => {
  Object.assign(questionForm, {
    title: '',
    content: '',
    category: '',
    difficulty: '',
    answer: '',
    tags: ''
  })
  questionFormRef.value?.clearValidate()
}

const openImportDialog = () => {
  ElMessage.info('批量导入功能正在开发中...')
}

const getPreview = (content) => {
  return content.length > 50 ? content.substring(0, 50) + '...' : content
}

const getDifficultyType = (difficulty) => {
  const types = {
    1: 'success',
    2: 'warning',
    3: 'danger'
  }
  return types[difficulty] || 'info'
}

const getDifficultyText = (difficulty) => {
  const texts = {
    1: '简单',
    2: '中等',
    3: '困难'
  }
  return texts[difficulty] || '未知'
}

const formatDate = (dateString) => {
  const date = new Date(dateString)
  return date.toLocaleDateString('zh-CN', {
    year: 'numeric',
    month: '2-digit',
    day: '2-digit',
    hour: '2-digit',
    minute: '2-digit'
  })
}

const loadQuestions = async () => {
  loading.value = true
  try {
    await questionStore.fetchQuestions()
  } finally {
    loading.value = false
  }
}

// 生命周期
onMounted(() => {
  loadQuestions()
  categoryStore.fetchCategories() // 加载分类数据
  
  // 监听窗口大小变化
  window.addEventListener('resize', () => {
    isMobile.value = window.innerWidth < 768
  })
})
</script>

<style scoped>
.question-bank-container {
  padding: 0;
}

/* 操作卡片 */
.operation-card {
  margin-bottom: 20px;
}

.operation-content {
  display: flex;
  align-items: center;
  justify-content: space-between;
  flex-wrap: wrap;
  gap: 16px;
}

.search-filters {
  display: flex;
  align-items: center;
  gap: 12px;
  flex: 1;
  flex-wrap: wrap;
}

.search-input {
  width: 300px;
  min-width: 200px;
}

.filter-select {
  width: 120px;
}

.operation-buttons {
  display: flex;
  gap: 8px;
  flex-shrink: 0;
}

/* 表格卡片 */
.table-card {
  margin-bottom: 20px;
}

.question-table {
  margin-bottom: 20px;
}

.question-title-cell {
  padding: 8px 0;
}

.question-title {
  font-size: 14px;
  font-weight: 500;
  color: #303133;
  margin: 0 0 4px 0;
  line-height: 1.4;
}

.question-preview {
  font-size: 12px;
  color: #909399;
  margin: 0;
  line-height: 1.3;
}

.review-count {
  font-weight: 500;
  color: #606266;
}

.avg-score {
  font-weight: 500;
}

.update-time {
  font-size: 12px;
  color: #909399;
}

.action-buttons {
  display: flex;
  gap: 4px;
  justify-content: center;
}

.action-buttons .el-button {
  margin: 0;
}

/* 分页 */
.pagination-wrapper {
  display: flex;
  justify-content: center;
  padding-top: 20px;
}

/* 对话框 */
.question-form .full-width {
  width: 100%;
}

.dialog-footer {
  display: flex;
  justify-content: flex-end;
  gap: 12px;
}

/* 预览对话框 */
.preview-content {
  padding: 0;
}

.preview-header {
  margin-bottom: 24px;
  padding-bottom: 16px;
  border-bottom: 1px solid #e4e7ed;
}

.preview-title {
  font-size: 18px;
  font-weight: 600;
  color: #303133;
  margin: 0 0 12px 0;
}

.preview-meta {
  display: flex;
  gap: 8px;
}

.preview-body h4 {
  font-size: 14px;
  font-weight: 600;
  color: #303133;
  margin: 20px 0 8px 0;
}

.preview-body h4:first-child {
  margin-top: 0;
}

.content-text,
.answer-text {
  line-height: 1.6;
  color: #606266;
  white-space: pre-wrap;
  background: #fafafa;
  padding: 12px;
  border-radius: 4px;
  margin-bottom: 16px;
}

.tags-list {
  display: flex;
  flex-wrap: wrap;
  gap: 8px;
}

.tag-item {
  margin: 0;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .operation-content {
    flex-direction: column;
    align-items: stretch;
  }

  .search-filters {
    flex-direction: column;
    align-items: stretch;
  }

  .search-input,
  .filter-select {
    width: 100%;
  }

  .operation-buttons {
    justify-content: center;
  }

  .question-table {
    font-size: 12px;
  }

  .action-buttons {
    flex-direction: column;
    gap: 4px;
  }

  .action-buttons .el-button {
    width: 32px;
    height: 32px;
  }

  .pagination-wrapper {
    overflow-x: auto;
  }
}

@media (max-width: 480px) {
  .question-title {
    font-size: 13px;
  }

  .question-preview {
    font-size: 11px;
  }
}

/* 评分颜色 */
.score-1, .score-0 { color: #f56c6c; }
.score-2 { color: #e6a23c; }
.score-3 { color: #409eff; }
.score-4, .score-5 { color: #67c23a; }
</style>